package cn.casair.service.impl;

import cn.casair.aop.logging.enums.BusinessTypeEnum;
import cn.casair.aop.logging.enums.ModuleTypeEnum;
import cn.casair.common.enums.*;
import cn.casair.common.errors.CommonException;
import cn.casair.common.utils.*;
import cn.casair.common.utils.excel.ZlSheet;
import cn.casair.domain.*;
import cn.casair.dto.*;
import cn.casair.ncc.dto.NccVoucherDTO;
import cn.casair.dto.nc.param.AuxiliaryDTO;
import cn.casair.dto.nc.param.VoucherDetailDTO;
import cn.casair.dto.nc.param.VoucherHeadDTO;
import cn.casair.dto.nc.result.AuxiliaryResultDTO;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.mapper.HrBillInvoiceMapper;
import cn.casair.mapper.HrBillInvoiceRecordMapper;
import cn.casair.mapper.HrBillTotalMapper;
import cn.casair.mapper.HrFeeReviewMapper;
import cn.casair.repository.*;
import cn.casair.service.*;
import cn.casair.service.component.ecloud.ECloudComponent;
import cn.casair.service.util.SecurityUtils;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.spire.xls.FileFormat;
import com.spire.xls.Workbook;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开票申请服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class HrBillInvoiceServiceImpl extends ServiceImpl<HrBillInvoiceRepository, HrBillInvoice> implements HrBillInvoiceService {

    private final SysOperLogService sysOperLogService;
    private final HrBillInvoiceRepository hrBillInvoiceRepository;
    private final HrBillInvoiceMapper hrBillInvoiceMapper;
    private final HrClientService hrClientService;
    private final HrAppendixService hrAppendixService;
    private final HrBillInvoiceRecordService hrBillInvoiceRecordService;
    private final HrBillInvoiceRecordMapper hrBillInvoiceRecordMapper;
    private final RoleService roleService;
    private final HrApplyOpLogsService applyOpLogsService;
    private final RoleRepository roleRepository;
    private final HrMessageListService hrMessageListService;
    private final HrFeeReviewRepository hrFeeReviewRepository;
    private final HrBillTotalRepository hrBillTotalRepository;
    private final HrBillTotalMapper hrBillTotalMapper;
    private final HrArrivalRecordService hrArrivalRecordService;
    private final HrBillReimbursementApplyService hrBillReimbursementApplyService;
    private final UserRepository userRepository;
    private final HrBillRepository hrBillRepository;
    private final HrSealsRepository hrSealsRepository;
    private final ECloudComponent eCloudComponent;
    private final HrFeeReviewMapper hrFeeReviewMapper;
    private final HrBillReimbursementClientRepository hrBillReimbursementClientRepository;
    private final HrBillReimbursementApplyRepository hrBillReimbursementApplyRepository;
    private final HrClientRepository hrClientRepository;
    private final HrBillReimbursementApplyDetailRepository hrBillReimbursementApplyDetailRepository;
    private final NcAccountRepository ncAccountRepository;
    private final NcCustomerRepository ncCustomerRepository;
    private final HrArrivalRecordDetailRepository hrArrivalRecordDetailRepository;
    private final NcService ncService;
    private final HrBillService hrBillService;
    private final HrBillInvoiceReviewRepository hrBillInvoiceReviewRepository;
    private final HrPlatformAccountRepository hrPlatformAccountRepository;
    @Value("${file.temp-path}")
    private String tempPath;
    @Value("${constant.fontPath}")
    private String fontPath;
    @Value("${ncc.accountbook}")
    private String ncAccountbook;

    private static boolean handleMergeCell(Sheet sheet, Map<String, String> hashMap, String cellValue, String value1, int i, int j) {
        boolean flag = false;
        String mapValue = hashMap.get(cellValue);
        if (mapValue != null) {
            String[] split = mapValue.split(",");
            int rowIndex = Integer.parseInt(split[0]);
            int columnIndex = Integer.parseInt(split[1]);
            if (rowIndex == i) {//在同一行
                if (value1.equals("1")) {
                    if (columnIndex == j) {
                        flag = true;
                    } else {
                        Row upperRow = sheet.getRow(i - 1);
                        if (upperRow != null) {
                            Cell rowCell1 = upperRow.getCell(j);
                            if (rowCell1.getCellType() == CellType.NUMERIC) {
                                rowCell1.setCellType(CellType.STRING);
                            }
                            if (rowCell1.getStringCellValue().equals(cellValue)) {
                                flag = true;
                            }
                        }
                    }
                } else {
                    flag = true;
                }
            } else {
                if (columnIndex == j) {
                    flag = true;
                }
            }
        }
        hashMap.put(cellValue, i + "," + j);
        return flag;
    }

    public static org.apache.poi.ss.usermodel.Workbook getWorkbook(InputStream inStr, String fileName) throws Exception {
        org.apache.poi.ss.usermodel.Workbook wb = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (".xls".equals(fileType)) {
            wb = new HSSFWorkbook(inStr);  //2003-
        } else if (".xlsx".equals(fileType)) {
            wb = new XSSFWorkbook(inStr);  //2007+
        } else {
            throw new Exception("解析的文件格式有误！");
        }
        return wb;
    }

    /**
     * 创建开票申请
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @Override
    public HrBillInvoiceDTO createHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO) {
        log.info("Create new HrBillInvoice:{}", hrBillInvoiceDTO);
        if (hrBillInvoiceDTO.getFlag()) {
            hrBillInvoiceDTO.setApplyId(SecurityUtils.getCurrentUser().get().getId());
        }
        // 处理合计金额大写
        hrBillInvoiceDTO.setTotalAmountCn(Convert.digitToChinese(hrBillInvoiceDTO.getTotalAmount()));

        hrBillInvoiceDTO.setLastModifiedDate(LocalDateTime.now());
        HrBillInvoice hrBillInvoice = this.hrBillInvoiceMapper.toEntity(hrBillInvoiceDTO);
        this.hrBillInvoiceRepository.insert(hrBillInvoice);
        hrBillInvoiceDTO.setId(hrBillInvoice.getId());

        // 保存附件和发票明细数据
        saveRecordAndFile(hrBillInvoiceDTO);

//        //如果审批状态approveStatus传过来0，则不需要走审批推送，是暂存。如果传入1，则需要进入审批,
//        if (hrBillInvoiceDTO.getApproveStatus() == 1) {
//            // todo 做小程序推送（暂无此需求）
//        }
        HrBillInvoiceDTO hrBillInvoiceDTO1 = this.hrBillInvoiceMapper.toDto(hrBillInvoice);
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.BILL_INVOICE.getValue(),
            BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrBillInvoiceDTO),
            HrBillInvoiceDTO.class,
            null,
            JSON.toJSONString(hrBillInvoiceDTO1)
        );
        return hrBillInvoiceDTO1;
    }

    /**
     * 新增开票申请
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @Override
    public HrBillInvoiceDTO insertHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO) {
        List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceDTO.getInvoiceRecords();
        if (invoiceRecords == null || invoiceRecords.isEmpty()) {
            throw new CommonException("开票明细不能为空！");
        }
        Set<String> feeReviewIds = new HashSet<>();
        Set<HrBillInvoiceRecordDTO> invoiceTypes = new HashSet<>();
        hrBillInvoiceDTO.getInvoiceRecords().forEach(invoiceRecordDTO -> {
            invoiceTypes.add(invoiceRecordDTO);
            List<String> reviewIds = invoiceRecordDTO.getFeeReviewIds();
            if (reviewIds != null && !reviewIds.isEmpty()) {
                feeReviewIds.addAll(reviewIds);
            }
        });
        if (!feeReviewIds.isEmpty()) {
            //处理之前已发起的可开发票的数据和审批拒绝的开票记录
            List<HrBillInvoiceDTO> invoiceDTOList = hrBillInvoiceRepository.findByReviewId(new ArrayList<>(feeReviewIds), BillInvoiceApproveEnums.InvoiceIsDefault.PROCESS_AUTOMATION.getKey());
            if (!invoiceDTOList.isEmpty()) {
                List<String> invoiceIdList1 = invoiceDTOList.stream().filter(lst -> lst.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())).map(HrBillInvoiceDTO::getId).collect(Collectors.toList());
                if (!invoiceIdList1.isEmpty()) {
                    hrBillInvoiceRepository.updateBeforeInvoice(invoiceIdList1, BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey(), 1);
                }
                List<String> invoiceIdList2 = invoiceDTOList.stream().filter(lst -> lst.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())).map(HrBillInvoiceDTO::getId).collect(Collectors.toList());
                if (!invoiceIdList2.isEmpty()) {
                    hrBillInvoiceRepository.updateBeforeInvoice(invoiceIdList2, BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey(), 0);
                }
                List<String> invoiceIds = invoiceDTOList.stream().map(HrBillInvoiceDTO::getId).collect(Collectors.toList());
                hrBillInvoiceRecordService.updateStateByInvoiceId(invoiceIds, new ArrayList<>(invoiceTypes), BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
            }
            List<HrBillInvoiceDTO> invoiceDTOS = hrBillInvoiceRepository.findByReviewId(new ArrayList<>(feeReviewIds), BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey());
            if (!invoiceDTOS.isEmpty()) {
                List<String> invoiceIds = invoiceDTOS.stream().map(HrBillInvoiceDTO::getId).collect(Collectors.toList());
                int updateStateByInvoiceId = hrBillInvoiceRecordService.updateStateByInvoiceId(invoiceIds, new ArrayList<>(invoiceTypes), BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                if (updateStateByInvoiceId != 0) {
                    hrBillInvoiceRepository.updateBeforeInvoice(invoiceIds, BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey(), 1);
                }
            }
            hrBillInvoiceDTO.setIsDefault(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey());
        } else {
            hrBillInvoiceDTO.setIsDefault(BillInvoiceApproveEnums.InvoiceIsDefault.CUSTOMIZE.getKey());
        }
        HrBillInvoiceDTO invoice = this.createHrBillInvoice(hrBillInvoiceDTO);
        // 保存审批记录
        applyOpLogsService.saveHrApplyOpLogsPhaseTWO(invoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(), "发起开票申请 ", null, ServiceCenterEnum.INVOICE_APPLY.getKey());
        return invoice;
    }

    /**
     * 流程自动化可开发票创建开票记录
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @Override
    public HrBillInvoiceDTO saveHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO) {
        boolean flag = false;
        List<HrFeeReview> feeReviewList = hrFeeReviewRepository.findFeeReviewByInvoiceId(hrBillInvoiceDTO.getId());
        if (!feeReviewList.isEmpty()) {
            hrBillInvoiceDTO.setFeeReviewId(feeReviewList.get(0).getId());
        }
        List<HrBillInvoiceRecordDTO> choiceInvoiceRecords = hrBillInvoiceDTO.getInvoiceRecords();//选择开票内容
        List<HrBillInvoiceRecordDTO> checkOnRecordList = choiceInvoiceRecords.stream().filter(lst -> lst.getCheckOn() != null && lst.getCheckOn() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkOnRecordList)) {
            throw new CommonException("选中明细不能为空！");
        }
        List<Integer> collect = checkOnRecordList.stream().map(HrBillInvoiceRecordDTO::getState).distinct().collect(Collectors.toList());
        if (collect.size() == 1) {//第一次发起
            Integer state = collect.get(0);
            if (state.equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())) {//选中明细第一次发起暂存
                this.saveInvoiceRecord(hrBillInvoiceDTO, choiceInvoiceRecords, false);
            } else if (state.equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())) {
                List<String> collect1 = checkOnRecordList.stream().map(HrBillInvoiceRecordDTO::getLevelId).collect(Collectors.toList());
                List<HrBillInvoiceRecord> hrBillInvoiceRecords = hrBillInvoiceRecordService.listByIds(collect1);//获取选中数据的开票记录明细
                if (hrBillInvoiceRecords == null || hrBillInvoiceRecords.isEmpty()) {
                    this.saveInvoiceRecord(hrBillInvoiceDTO, choiceInvoiceRecords, false);
                } else {
                    List<String> invoiceIds = hrBillInvoiceRecords.stream().map(HrBillInvoiceRecord::getInvoiceId).distinct().collect(Collectors.toList());//获取选中数据的开票记录ID
                    List<HrBillInvoiceRecord> list = hrBillInvoiceRecordService.list(new QueryWrapper<HrBillInvoiceRecord>().in("invoice_id", invoiceIds));
                    //判断选中的明细是否在同一开票记录中 并且重新发起的数据和审核未通过的数据一致
                    if (invoiceIds.size() == 1 && list.size() == checkOnRecordList.size()) {//是 修改记录和明细
                        this.updateHrBillInvoiceRecord(hrBillInvoiceDTO, invoiceIds.get(0), checkOnRecordList);
                        flag = true;
                    } else {//否 重新发起记录 将上一次的记录明细状态改为 重新锁定
                        hrBillInvoiceRecordService.updateState(collect1, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                        this.saveInvoiceRecord(hrBillInvoiceDTO, choiceInvoiceRecords, false);
                    }
                }
            }
        } else {
            List<HrBillInvoiceRecordDTO> billInvoiceRecordDTOS = choiceInvoiceRecords.stream().filter(ls -> ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
            List<String> collect1 = billInvoiceRecordDTOS.stream().map(HrBillInvoiceRecordDTO::getLevelId).collect(Collectors.toList());
            hrBillInvoiceRecordService.updateState(collect1, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
            this.saveInvoiceRecord(hrBillInvoiceDTO, choiceInvoiceRecords, false);
        }
        //修改可开发票锁定状态
        QueryWrapper<HrBillInvoiceRecord> qw = new QueryWrapper<>();
        qw.eq("invoice_id", hrBillInvoiceDTO.getId());
        List<HrBillInvoiceRecord> hrBillInvoiceRecords = hrBillInvoiceRecordService.list(qw);
        List<HrBillInvoiceRecord> notLockedRecordList = hrBillInvoiceRecords.stream().filter(ls -> ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey())
            || ls.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notLockedRecordList)) {
            hrBillInvoiceDTO.setInvoiceLockState(BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey());
        }
        //修改可开发票金额
        double totalAmount = hrBillInvoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
        double taxAmount = hrBillInvoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTaxAmount())).getSum();
        hrBillInvoiceDTO.setTotalAmount(totalAmount)
            .setTaxAmount(taxAmount)
            .setTotalAmountCn(Convert.digitToChinese(totalAmount))
            .setNoticeRoles(null).setRemark(null);
        //修改可开发票
        hrBillInvoiceRepository.updateById(hrBillInvoiceMapper.toEntity(hrBillInvoiceDTO));
        this.sysOperLogService.insertSysOperLog(
            ModuleTypeEnum.BILL_INVOICE.getValue(),
            flag ? BusinessTypeEnum.INSERT.getKey() : BusinessTypeEnum.INSERT.getKey(),
            JSON.toJSONString(hrBillInvoiceDTO),
            HrBillInvoiceDTO.class,
            null,
            JSON.toJSONString(hrBillInvoiceDTO)
        );
        return hrBillInvoiceDTO;
    }

    /**
     * 生成凭证
     * 如果用户选择已回款，验证到账金额和开票金额是否一致，如果一致，凭证带上银行存款和应收账款；否则报错
     * 如果用户选择未回款，凭证不带上银行存款和应收账款
     *
     * @param hrBillInvoiceDTO
     */
    @Override
    public void createVoucher(HrBillInvoiceDTO hrBillInvoiceDTO) {
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillInvoice hrBillInvoice = getById(hrBillInvoiceDTO.getId());
        if (hrBillInvoice.getInvoiceStatus() == 0) {
            throw new CommonException("发票状态为未开票，不能生成凭证！");
        }
        // 验证参数
        if (StringUtils.isBlank(hrBillInvoiceDTO.getAttachment())) {
            throw new CommonException("凭证的附件张数不能为空！");
        }
        if (StringUtils.isBlank(hrBillInvoiceDTO.getPrepareddate())) {
            throw new CommonException("凭证的制单日期不能为空！");
        }
        if (UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {
            // 验证会计用户的制单人编码是否已经填写
            UserDTO user = userRepository.getUserInFor(userDTO.getId());
            if (user == null) {
                throw new CommonException("用户不存在或者被禁用！");
            }
            if (StringUtils.isBlank(user.getBillmaker())) {
                throw new CommonException("用户的NC制单人编码尚未配置！");
            }
            VoucherHeadDTO voucherHead = new VoucherHeadDTO();
            voucherHead.setAccountbook(ncAccountbook);
            voucherHead.setVouchertype("01");
            voucherHead.setBillmaker(user.getBillmaker());
            voucherHead.setAttachment(hrBillInvoiceDTO.getAttachment());
            voucherHead.setPrepareddate(hrBillInvoiceDTO.getPrepareddate());
            voucherHead.setPeriod(hrBillInvoiceDTO.getPayMonthly().toString());

            List<VoucherDetailDTO> voucherDetailList = new ArrayList<>();
            NcCustomerDTO ncCustomer = ncCustomerRepository.findByClientId(hrBillInvoice.getClientId());
            if (ncCustomer == null) {
                throw new CommonException("客户与NC客户的关联未设置！");
            }
            List<NcAccountDTO> ncAccountList1 = ncAccountRepository.findByTypeAndContentAndDebt(1, "应收账款", "D");
            if (ncAccountList1 == null || ncAccountList1.isEmpty()) {
                throw new CommonException("借方向的应收账款科目未查询到！");
            }
            NcAccountDTO ncAccount1 = ncAccountList1.get(0);
            ncAccount1.setAmount(String.format("%.2f", hrBillInvoice.getTotalAmount()));
            voucherDetailList.add(this.generateVoucherDetail(ncAccount1, ncCustomer));
            if (hrBillInvoice.getTaxAmount() != null && hrBillInvoice.getTaxAmount() != 0d) {
                List<NcAccountDTO> ncAccountList2 = ncAccountRepository.findByTypeAndContentAndDebt(1, "销项税额", "C");
                if (ncAccountList2 == null || ncAccountList2.isEmpty()) {
                    throw new CommonException("销项税额的科目未查询到！");
                }
                NcAccountDTO ncAccount2 = ncAccountList2.get(0);
                ncAccount2.setAmount(String.format("%.2f", hrBillInvoice.getTaxAmount()));
                voucherDetailList.add(this.generateVoucherDetail(ncAccount2, ncCustomer));
            }
            // 查询发票明细列表
            List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceRecordService.getByInvoiceId(hrBillInvoiceDTO.getId());
            if (invoiceRecords == null || invoiceRecords.isEmpty()) {
                throw new CommonException("发票不包含生成凭证的分录项！");
            }

            // 如果是新增那种，并且用户选择合并金额，需要把同类内容的合并
            if (hrBillInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey()) && hrBillInvoice.getIsMerge().equals(1)) {
                Set<String> contentTaxSet = new LinkedHashSet<>();
                invoiceRecords.forEach(item -> {
                    contentTaxSet.add(item.getContent() + "-" + item.getTaxRate());
                });
                Map<String, List<HrBillInvoiceRecordDTO>> map = invoiceRecords.stream().collect(Collectors.groupingBy(item -> (item.getContent() + "-" + item.getTaxRate())));
                List<HrBillInvoiceRecordDTO> invoiceRecords2 = new ArrayList<>();
                contentTaxSet.forEach(contentTax -> {
                    HrBillInvoiceRecordDTO billInvoiceRecordDTO = new HrBillInvoiceRecordDTO();
                    List<HrBillInvoiceRecordDTO> list = map.get(contentTax);
                    HrBillInvoiceRecordDTO first = list.get(0);
                    Double totalAmount = 0.00d;
                    billInvoiceRecordDTO.setContent(first.getContent());
                    billInvoiceRecordDTO.setTaxRate(first.getTaxRate());
                    for (HrBillInvoiceRecordDTO recordDTO : list) {
                        totalAmount += recordDTO.getTotalAmount();
                    }
                    billInvoiceRecordDTO.setTitle(first.getTitle());
                    billInvoiceRecordDTO.setTotalAmount(totalAmount);
                    billInvoiceRecordDTO.setTaxAmount(totalAmount * first.getTaxRate());
                    billInvoiceRecordDTO.setNoTaxAmount(billInvoiceRecordDTO.getTotalAmount() - billInvoiceRecordDTO.getTaxAmount());
                    invoiceRecords2.add(billInvoiceRecordDTO);
                });
                invoiceRecords = invoiceRecords2;
            }
            invoiceRecords.forEach(item -> {
                NcAccountDTO ncAccountDTO = null;
                if (StringUtils.isBlank(item.getContent())) {
                    throw new CommonException("发票包含内容为空的分录项！");
                }
                String content = BillInvoiceApproveEnums.InvoiceContent.getValueByKey(item.getContent());
                // 技术服务费有两种：   1、外包（中石化）--十建项目服务费收入********；2、其他外包--外包服务费收入********
                List<NcAccountDTO> accounts = ncAccountRepository.findByTypeAndContentAndDebt(1, content, "C");
                if (accounts == null || accounts.isEmpty()) {
                    throw new CommonException("生成凭证不支持发票内容：" + content + "！");
                }
                if (accounts.size() == 1) {
                    ncAccountDTO = accounts.get(0);
                } else {
                    // 如果存在名称一样的多个科目，优先使用与该客户有关联的
                    List<NcAccountDTO> accountDTOS = accounts.stream().filter(accountDTO1 -> StringUtils.isNotBlank(accountDTO1.getClientId()) && accountDTO1.getClientId().equals(hrBillInvoice.getClientId())).collect(Collectors.toList());
                    if (accountDTOS.isEmpty()) {
                        List<NcAccountDTO> accountDTOS2 = accounts.stream().filter(accountDTO1 -> StringUtils.isBlank(accountDTO1.getClientId())).collect(Collectors.toList());
                        if (accountDTOS2.isEmpty()) {
                            throw new CommonException("发票内容:" + content + "，没有找到对应的科目信息！");
                        } else {
                            ncAccountDTO = accountDTOS2.get(0);
                        }
                    } else {
                        ncAccountDTO = accountDTOS.get(0);
                    }
                }
                // 不含税金额
                ncAccountDTO.setAmount(String.format("%.2f", item.getNoTaxAmount()));
                ncAccountDTO.setTaxRate(item.getTaxRate());
                voucherDetailList.add(this.generateVoucherDetail(ncAccountDTO, ncCustomer));
            });
            // TODO Jigsaw - 2025/8/7 ：回款在到账记录里同步
            //  根据是否已回款 如果有回款，会生成带回款的凭证（借-银行存款+贷-应收账款）
            if (hrBillInvoiceDTO.getIsRepayment()) {
                BigDecimal bankAmount = BigDecimal.ZERO;
                List<NcAccountDTO> ncAccountList3 = ncAccountRepository.findByTypeAndContentAndDebt(1, "银行存款", "D");
                if (ncAccountList3 == null || ncAccountList3.isEmpty()) {
                    throw new CommonException("银行存款的科目未查询到！");
                }
                NcAccountDTO bankAccount = ncAccountList3.get(0);
                // 获取到账记录明细
                List<HrArrivalRecordDetailDTO> detailList = this.hrArrivalRecordDetailRepository.getListByBillInvoiceId(hrBillInvoice.getId());
                if (detailList == null || detailList.isEmpty()) {
                    log.info("用户选择已回款，到账记录不存在，默认生成一条为1的银行存款");
                    // 获取默认的银行 如果有37101988141051001067 使用这个； 如果没有，使用第一个账号
                    String platformAccount = "37101988141051001067";
                    List<HrPlatformAccountDTO> platformAccountList = this.hrPlatformAccountRepository.findListByPlatformType("4");
                    if (platformAccountList != null && !platformAccountList.isEmpty()) {
                        List<HrPlatformAccountDTO> platformAccounts = platformAccountList.stream().filter(item -> item.getAccountNumber().equals("37101988141051001067")).collect(Collectors.toList());
                        if (platformAccounts == null || platformAccounts.isEmpty()) {
                            platformAccount = platformAccountList.get(0).getAccountNumber();
                        }
                        // 没有到账记录时，设置默认值为1
                        bankAmount = BigDecimal.ONE;
                        NcAccountDTO ncAccount3 = new NcAccountDTO();
                        BeanUtils.copyProperties(bankAccount, ncAccount3);
                        ncAccount3.setAmount(bankAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        ncAccount3.setAccountNumber(platformAccount);
                        voucherDetailList.add(this.generateVoucherDetail(ncAccount3, ncCustomer));
                        log.info("用户选择已回款，到账记录不存在，默认生成一条为1的银行存款，银行账号使用{}", platformAccount);
                    } else {
                        log.info("用户选择已回款，到账记录不存在，默认生成一条为1的银行存款，没找到默认的银行账号", platformAccount);
                    }
                } else {
                    log.info("用户选择已回款，找到到账记录");
                    for (HrArrivalRecordDetailDTO detailDTO : detailList) {
                        if (StringUtils.isBlank(detailDTO.getAccountNumber())) {
                            throw new CommonException("发票对应的到账记录明细，存在没有配置银行账号！");
                        }
                        if (detailDTO.getArrivalAmount() == null) {
                            throw new CommonException("发票对应的到账记录明细，存在没有配置到账金额！");
                        }
                        if (detailDTO.getArrivalAmount().compareTo(BigDecimal.ZERO) == 1) {
                            NcAccountDTO ncAccount3 = new NcAccountDTO();
                            BeanUtils.copyProperties(bankAccount, ncAccount3);
                            ncAccount3.setAmount(detailDTO.getArrivalAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            ncAccount3.setAccountNumber(detailDTO.getAccountNumber());
                            voucherDetailList.add(this.generateVoucherDetail(ncAccount3, ncCustomer));
                            bankAmount = bankAmount.add(detailDTO.getArrivalAmount());
                        }
                    }
                }
                // 应收账款 贷
                List<NcAccountDTO> ncAccountList4 = ncAccountRepository.findByTypeAndContentAndDebt(1, "应收账款", "C");
                if (ncAccountList4 == null || ncAccountList4.isEmpty()) {
                    throw new CommonException("贷方向的应收账款的科目未查询到！");
                }
                NcAccountDTO ncAccount4 = ncAccountList4.get(0);
                ncAccount4.setAmount(String.format("%.2f", bankAmount));
                voucherDetailList.add(this.generateVoucherDetail(ncAccount4, ncCustomer));
                log.info("用户选择已回款，应收账款，bankAmount={}", bankAccount);
            }
            // 调用NC生成凭证接口
            log.info("开票记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={}", voucherHead, voucherDetailList);
            NcResultDTO voucherResult = ncService.createVoucher(voucherHead, voucherDetailList);
            log.info("开票记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
            // 处理一下
            if (voucherResult.getErrorNo().equals("1")) {
                log.error("开票记录 调用NC生成凭证接口，参数：voucherHead={},voucherDetailList={},返回值为{}", voucherHead, voucherDetailList, voucherResult);
                throw new CommonException("调用NC系统生成凭证失败，原因：" + voucherResult.getMessage());
            }
            hrBillInvoice.setNcVoucher(voucherResult.getVoucherNum());
            // 凭证状态改为已开
            hrBillInvoice.setAccountingVoucherStatus(1);
            saveOrUpdate(hrBillInvoice);
        } else {
            throw new CommonException("只有会计才能生成凭证，您无操作权限");
        }
    }

    @Override
    public void createVoucherNcc(HrBillInvoiceDTO hrBillInvoiceDTO) {
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillInvoice hrBillInvoice = getById(hrBillInvoiceDTO.getId());
        if (hrBillInvoice.getInvoiceStatus() == 0) {
            throw new CommonException("发票状态为未开票，不能生成凭证！");
        }
        // 验证参数
        if (StringUtils.isBlank(hrBillInvoiceDTO.getAttachment())) {
            throw new CommonException("凭证的附件张数不能为空！");
        }
        if (StringUtils.isBlank(hrBillInvoiceDTO.getPrepareddate())) {
            throw new CommonException("凭证的制单日期不能为空！");
        }
        if (UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {
            // 验证会计用户的制单人编码是否已经填写
            UserDTO user = userRepository.getUserInFor(userDTO.getId());
            if (user == null) {
                throw new CommonException("用户不存在或者被禁用！");
            }
            if (StringUtils.isBlank(user.getBillmaker())) {
                throw new CommonException("用户的NC制单人编码尚未配置！");
            }

            NccVoucherDTO nccVoucherDTO = new NccVoucherDTO();
            nccVoucherDTO.setAccbookCode(ncAccountbook);
            nccVoucherDTO.setPrepareddate(hrBillInvoiceDTO.getPrepareddate());
            nccVoucherDTO.setYear(hrBillInvoiceDTO.getPayYear().toString());
            nccVoucherDTO.setPeriod(hrBillInvoiceDTO.getPayMonthly().toString());
            nccVoucherDTO.setPrepared(user.getBillmaker());

            List<NccVoucherDTO.Detail> voucherDetailList = new ArrayList<>();
            NcCustomerDTO ncCustomer = ncCustomerRepository.findByClientId(hrBillInvoice.getClientId());
            if (ncCustomer == null) {
                throw new CommonException("客户与NC客户的关联未设置！");
            }
            // 1. 根据发票总金额生成，借应收账款
            List<NcAccountDTO> ncAccountList1 = ncAccountRepository.findByTypeAndContentAndDebt(1, "应收账款", "D");
            if (ncAccountList1 == null || ncAccountList1.isEmpty()) {
                throw new CommonException("借方向的应收账款科目未查询到！");
            }
            NcAccountDTO ncAccount1 = ncAccountList1.get(0);
            ncAccount1.setAmount(String.format("%.2f", hrBillInvoice.getTotalAmount()));
            voucherDetailList.add(this.generateNccVoucherDetail(ncAccount1, ncCustomer));

            // 查询发票明细列表
            List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceRecordService.getByInvoiceId(hrBillInvoiceDTO.getId());
            if (invoiceRecords == null || invoiceRecords.isEmpty()) {
                throw new CommonException("发票不包含生成凭证的分录项！");
            }


            // 如果是新增那种，并且用户选择合并金额，需要把同类内容的合并
            if (hrBillInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey()) && hrBillInvoice.getIsMerge().equals(1)) {
                Set<String> contentTaxSet = new LinkedHashSet<>();
                invoiceRecords.forEach(item -> {
                    contentTaxSet.add(item.getContent() + "-" + item.getTaxRate());
                });
                Map<String, List<HrBillInvoiceRecordDTO>> map = invoiceRecords.stream().collect(Collectors.groupingBy(item -> (item.getContent() + "-" + item.getTaxRate())));
                List<HrBillInvoiceRecordDTO> invoiceRecords2 = new ArrayList<>();
                contentTaxSet.forEach(contentTax -> {
                    HrBillInvoiceRecordDTO billInvoiceRecordDTO = new HrBillInvoiceRecordDTO();
                    List<HrBillInvoiceRecordDTO> list = map.get(contentTax);
                    HrBillInvoiceRecordDTO first = list.get(0);
                    Double totalAmount = 0.00d;
                    billInvoiceRecordDTO.setContent(first.getContent());
                    billInvoiceRecordDTO.setTaxRate(first.getTaxRate());
                    for (HrBillInvoiceRecordDTO recordDTO : list) {
                        totalAmount += recordDTO.getTotalAmount();
                    }
                    billInvoiceRecordDTO.setTitle(first.getTitle());
                    billInvoiceRecordDTO.setTotalAmount(totalAmount);
                    billInvoiceRecordDTO.setTaxAmount(totalAmount * first.getTaxRate());
                    billInvoiceRecordDTO.setNoTaxAmount(billInvoiceRecordDTO.getTotalAmount() - billInvoiceRecordDTO.getTaxAmount());
                    invoiceRecords2.add(billInvoiceRecordDTO);
                });
                invoiceRecords = invoiceRecords2;
            }
            invoiceRecords.forEach(item -> {
                NcAccountDTO ncAccountDTO = null;
                if (StringUtils.isBlank(item.getContent())) {
                    throw new CommonException("发票包含内容为空的分录项！");
                }
                String content = BillInvoiceApproveEnums.InvoiceContent.getValueByKey(item.getContent());
                // 技术服务费有两种：   1、外包（中石化）--十建项目服务费收入********；2、其他外包--外包服务费收入********
                List<NcAccountDTO> accounts = ncAccountRepository.findByTypeAndContentAndDebt(1, content, "C");
                if (accounts == null || accounts.isEmpty()) {
                    throw new CommonException("生成凭证不支持发票内容：" + content + "！");
                }
                if (accounts.size() == 1) {
                    ncAccountDTO = accounts.get(0);
                } else {
                    // 如果存在名称一样的多个科目，优先使用与该客户有关联的
                    List<NcAccountDTO> accountDTOS = accounts.stream().filter(accountDTO1 -> StringUtils.isNotBlank(accountDTO1.getClientId()) && accountDTO1.getClientId().equals(hrBillInvoice.getClientId())).collect(Collectors.toList());
                    if (accountDTOS.isEmpty()) {
                        List<NcAccountDTO> accountDTOS2 = accounts.stream().filter(accountDTO1 -> StringUtils.isBlank(accountDTO1.getClientId())).collect(Collectors.toList());
                        if (accountDTOS2.isEmpty()) {
                            throw new CommonException("发票内容:" + content + "，没有找到对应的科目信息！");
                        } else {
                            ncAccountDTO = accountDTOS2.get(0);
                        }
                    } else {
                        ncAccountDTO = accountDTOS.get(0);
                    }
                }
                // 不含税金额
                ncAccountDTO.setAmount(String.format("%.2f", item.getNoTaxAmount()));
                ncAccountDTO.setTaxRate(item.getTaxRate());
                voucherDetailList.add(this.generateNccVoucherDetail(ncAccountDTO, ncCustomer));

                if (item.getTaxAmount() != null && item.getTaxAmount() != 0d) {
                    // TODO Jigsaw - 2025/8/18 ：区内代理费对应简易计税，其他非区内代理费的科目为销项税额
                    String ncContent = item.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.AGENCY_FEE_INCOME.getKey()) ? "简易计税" : "销项税额";
                    List<NcAccountDTO> ncAccountList2 = ncAccountRepository.findByTypeAndContentAndDebt(1, ncContent, "C");
                    if (ncAccountList2 == null || ncAccountList2.isEmpty()) {
                        throw new CommonException(ncContent + "的科目未查询到！");
                    }
                    NcAccountDTO ncAccount2 = ncAccountList2.get(0);
                    ncAccount2.setAmount(String.format("%.2f", hrBillInvoice.getTaxAmount()));
                    ncAccount2.setTaxRate(item.getTaxRate());
                    voucherDetailList.add(this.generateNccVoucherDetail(ncAccount2, ncCustomer));
                }

            });
            // TODO Jigsaw - 2025/8/7 ：回款在到账记录里同步
            NcResultDTO<NccVoucherDTO> voucherResult = ncService.createNccVoucher(nccVoucherDTO);
            // 处理一下
            if (voucherResult.getErrorNo().equals("1")) {
                throw new CommonException("调用NC系统生成凭证失败，原因：" + voucherResult.getMessage());
            }
            hrBillInvoice.setNcVoucher(voucherResult.getVoucherNum());
            // 凭证状态改为已开
            hrBillInvoice.setAccountingVoucherStatus(1);
            saveOrUpdate(hrBillInvoice);
        } else {
            throw new CommonException("只有会计才能生成凭证，您无操作权限");
        }
    }

    /**
     * 删除凭证
     *
     * @param hrBillInvoiceId
     */
    @Override
    public void deleteVoucher(String hrBillInvoiceId) {
        this.hrBillInvoiceRepository.deleteVoucher(hrBillInvoiceId);
    }

    /**
     * 处理结算单PDF
     *
     * @param ids
     */
    @Override
    public Map<String, Object> handleFeeReviewPDF(List<String> ids) {
        HrSeals hrSeals = hrSealsRepository.selectOne(new QueryWrapper<HrSeals>().eq("is_delete", 0).eq("seal_name", "财务专用章")
            .orderByDesc("created_date").last("LIMIT 1"));
        List<HrFeeReview> hrFeeReviewList = hrFeeReviewRepository.selectBatchIds(ids);
        //判断是否中石化账单
        List<String> billIds = Arrays.asList(hrFeeReviewList.get(0).getBillId().split(","));
        HrBill hrBill = hrBillRepository.selectOne(new QueryWrapper<HrBill>().in("id", billIds).orderByDesc("created_date").last("LIMIT 1"));
        Map<String, Object> map = new HashMap<>();
        for (HrFeeReview hrFeeReview : hrFeeReviewList) {
            HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(hrFeeReview.getDetailAppendixId());
            try {
                int x = 520;
                int y = 480;
                if (hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
                    x = 495;
                    y = 460;
                }
                String pdfFileUrl = this.excelToPdf(hrFeeReview, hrAppendixDTO, hrBill);
                HrAppendix uploadTempPdf = hrAppendixService.uploadTempPdf(pdfFileUrl);
                String contractNum = RandomUtil.generateNo();
                this.eCloudComponent.uploadContractUrl(contractNum, uploadTempPdf.getOriginName(), uploadTempPdf.getFileUrl());
                this.eCloudComponent.electronicBillSignature(contractNum, uploadTempPdf.getFileUrl(), hrSeals.getSignId(), x, y);
                HrAppendix hrAppendix = this.eCloudComponent.downloadContract(contractNum, CompanyInfoEnum.FIRST_PART_PHONE.getValue());
                hrFeeReview.setContractNum(contractNum);
                hrFeeReview.setDetailPdfAppendixId(hrAppendix.getId());
                hrFeeReviewRepository.updateById(hrFeeReview);
                map.put(hrFeeReview.getTitle(), hrAppendix);
            } catch (Exception e) {
                throw new CommonException("EXCEL转PDF异常！异常提示【" + e.getMessage() + "】");
            }
        }
        return map;
    }

    private VoucherDetailDTO generateVoucherDetail(NcAccountDTO accountDTO, NcCustomerDTO ncCustomer) {
        VoucherDetailDTO voucherDetail = new VoucherDetailDTO();
        voucherDetail.setDebt(accountDTO.getDebt());
        voucherDetail.setAccasoacode(accountDTO.getAccountcode());
        voucherDetail.setExplanation("开票申请-" + ncCustomer.getClientName());
        voucherDetail.setCurrency("CNY");
        voucherDetail.setAmount(accountDTO.getAmount());
        voucherDetail.setLocalexcrate("1.00");
        voucherDetail.setLocalamount(accountDTO.getAmount());
        voucherDetail.setGroupexcrate("1.00");
        voucherDetail.setGroupamount(accountDTO.getAmount());
        List<AuxiliaryDTO> auxiliarys = new ArrayList<>();
        List<AuxiliaryResultDTO> auxiliaryResultDTOS = JSONArray.parseArray(accountDTO.getAuxiliary(), AuxiliaryResultDTO.class);
        for (AuxiliaryResultDTO auxiliaryResult : auxiliaryResultDTOS) {
            AuxiliaryDTO auxiliaryDTO = new AuxiliaryDTO();
            auxiliaryDTO.setItemcode(auxiliaryResult.getAccasscode());
            // 0004客商
            if (auxiliaryResult.getAccasscode().equals("0004")) {
                auxiliaryDTO.setContentcode(ncCustomer.getNcCode());
            }
            //  999项目(自定义档案)  默认其他
            if (auxiliaryResult.getAccasscode().equals("999")) {
                auxiliaryDTO.setContentcode("17");
            }
            //  004税率
            if (auxiliaryResult.getAccasscode().equals("004")) {
                // TODO Jigsaw - 2025/8/18 ：税率的对应关系
                auxiliaryDTO.setContentcode(NcTaxRateEnum.getValueByKey(accountDTO.getTaxRate()));
            }
            //  0007现金流量项目  默认 收到的其他与经营活动有关的现金
            if (auxiliaryResult.getAccasscode().equals("0007")) {
                // TODO Jigsaw - 2025/8/18 ：回款的辅助核算
                auxiliaryDTO.setContentcode("1113");
            }
            // 0011银行账户
            if (auxiliaryResult.getAccasscode().equals("0011")) {
                // TODO Jigsaw - 2025/8/18 ：回款时需要的辅助核算
                auxiliaryDTO.setContentcode(accountDTO.getAccountNumber());
            }
            auxiliarys.add(auxiliaryDTO);
        }
        voucherDetail.setAuxiliarys(auxiliarys);
        return voucherDetail;
    }

    private NccVoucherDTO.Detail generateNccVoucherDetail(NcAccountDTO accountDTO, NcCustomerDTO ncCustomer) {
//        VoucherDetailDTO voucherDetail = new VoucherDetailDTO();
//        voucherDetail.setDebt(accountDTO.getDebt());
//        voucherDetail.setAccasoacode(accountDTO.getAccountcode());
//        voucherDetail.setExplanation("开票申请-" + ncCustomer.getClientName());
//        voucherDetail.setCurrency("CNY");
//        voucherDetail.setAmount(accountDTO.getAmount());
//        voucherDetail.setLocalexcrate("1.00");
//        voucherDetail.setLocalamount(accountDTO.getAmount());
//        voucherDetail.setGroupexcrate("1.00");
//        voucherDetail.setGroupamount(accountDTO.getAmount());
        NccVoucherDTO.Detail detail = new NccVoucherDTO.Detail();
        detail.setExplanation(accountDTO.getExplanation());
        detail.setAccountCode(accountDTO.getAccountcode());
        detail.setCurrtypeCode("CNY");
        detail.setAmount(new BigDecimal(accountDTO.getAmount()));
        if ("D".equals(accountDTO.getDebt())) {
            detail.setLocaldebitamount(new BigDecimal(accountDTO.getAmount()));
        } else if ("C".equals(accountDTO.getDebt())) {
            detail.setLocalcreditamount(new BigDecimal(accountDTO.getAmount()));
        } else {
            throw new CommonException("未知的借贷方向：" + accountDTO.getDebt());
        }
        List<NccVoucherDTO.Ass> auxiliarys = new ArrayList<>();
        List<AuxiliaryResultDTO> auxiliaryResultDTOS = JSONArray.parseArray(accountDTO.getAuxiliary(), AuxiliaryResultDTO.class);
        for (AuxiliaryResultDTO auxiliaryResult : auxiliaryResultDTOS) {
            NccVoucherDTO.Ass auxiliaryDTO = new NccVoucherDTO.Ass();
            auxiliaryDTO.setChecktypecode(auxiliaryResult.getAccasscode());
            // 0004客商
            if (auxiliaryResult.getAccasscode().equals("0004")) {
                auxiliaryDTO.setCheckvaluecode(ncCustomer.getNcCode());
            }
            //  999项目(自定义档案)  默认其他
            if (auxiliaryResult.getAccasscode().equals("999")) {
                auxiliaryDTO.setCheckvaluecode("17");
            }
            //  004税率
            if (auxiliaryResult.getAccasscode().equals("004")) {
                // TODO Jigsaw - 2025/8/18 ：税率的对应关系
                auxiliaryDTO.setCheckvaluecode(NcTaxRateEnum.getValueByKey(accountDTO.getTaxRate()));
            }
            //  0007现金流量项目  默认 收到的其他与经营活动有关的现金
            if (auxiliaryResult.getAccasscode().equals("0007")) {
                // TODO Jigsaw - 2025/8/18 ：回款的辅助核算
                auxiliaryDTO.setCheckvaluecode("1113");
            }
            // 0011银行账户
            if (auxiliaryResult.getAccasscode().equals("0011")) {
                // TODO Jigsaw - 2025/8/18 ：回款时需要的辅助核算
                auxiliaryDTO.setCheckvaluecode(accountDTO.getAccountNumber());
            }
            auxiliarys.add(auxiliaryDTO);
        }
//        voucherDetail.setAuxiliarys(auxiliarys);
        detail.setAss(auxiliarys);
        return detail;
    }

    /**
     * 修改开票记录和开票明细
     *
     * @param hrBillInvoiceDTO
     * @param invoiceId
     * @param checkOnRecordList
     */
    private void updateHrBillInvoiceRecord(HrBillInvoiceDTO hrBillInvoiceDTO, String invoiceId, List<HrBillInvoiceRecordDTO> checkOnRecordList) {
        //重新发起开票明细和上级可开发票明细状态修改为重新锁定
        List<String> ids = checkOnRecordList.stream().map(HrBillInvoiceRecordDTO::getId).collect(Collectors.toList());
        List<String> levelIds = checkOnRecordList.stream().map(HrBillInvoiceRecordDTO::getLevelId).collect(Collectors.toList());
        ids.addAll(levelIds);
        hrBillInvoiceRecordService.updateState(ids, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
        HrBillInvoice invoice = hrBillInvoiceMapper.toEntity(hrBillInvoiceDTO);
        invoice.setId(invoiceId)
            .setParentId(hrBillInvoiceDTO.getId())
            .setInvoiceState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
            .setLastModifiedDate(LocalDateTime.now());
        double totalAmount = checkOnRecordList.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
        double taxAmount = checkOnRecordList.stream().collect(Collectors.summarizingDouble(value -> value.getTaxAmount())).getSum();
        invoice.setTotalAmount(totalAmount)
            .setTaxAmount(taxAmount)
            .setTotalAmountCn(Convert.digitToChinese(totalAmount))
            .setApplyDate(LocalDate.now());
        hrBillInvoiceRepository.updateById(invoice);
        for (HrBillInvoiceRecordDTO invoiceRecord : checkOnRecordList) {
            invoiceRecord.setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
            HrBillInvoiceRecord oldRecord = hrBillInvoiceRecordMapper.toEntity(invoiceRecord);
            oldRecord.setLastModifiedDate(LocalDateTime.now());
            hrBillInvoiceRecordService.updateById(oldRecord);

            HrBillInvoiceRecord newRecord = hrBillInvoiceRecordMapper.toEntity(invoiceRecord);
            newRecord.setId(invoiceRecord.getLevelId()).setInvoiceId(invoice.getId());
            newRecord.setCreatedDate(LocalDateTime.now());
            hrBillInvoiceRecordService.updateById(newRecord);
        }
        // 保存附件关联
        if (hrBillInvoiceDTO.getFileIds() != null) {
            hrAppendixService.deleteByUnionId(hrBillInvoiceDTO.getId());
            hrBillInvoiceDTO.getFileIds().forEach(fileId -> {
                hrAppendixService.saveAppendixUnion(fileId, invoice.getId());
            });
        }
        // 保存审批记录
        applyOpLogsService.saveHrApplyOpLogsPhaseTWO(invoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
            "重新发起开票申请 ", null, ServiceCenterEnum.INVOICE_APPLY.getKey());
    }

    /**
     * 添加发票记录和发票明细数据
     *
     * @param hrBillInvoiceDTO
     * @param choiceInvoiceRecords
     * @param flag                 true审核未通过重新发起 false开票记录发起
     */
    public void saveInvoiceRecord(HrBillInvoiceDTO hrBillInvoiceDTO, List<HrBillInvoiceRecordDTO> choiceInvoiceRecords, Boolean flag) {
        //添加开票记录
        JWTUserDTO jwtUserDTO = SecurityUtils.getJwtUser();
        HrBillInvoiceDTO invoiceDTO = new HrBillInvoiceDTO();
        BeanUtils.copyProperties(hrBillInvoiceDTO, invoiceDTO);
        if (hrBillInvoiceDTO.getFlag()) {
            invoiceDTO.setApplyId(jwtUserDTO.getId());
        }
        String parentId = hrBillInvoiceDTO.getId();
        if (flag) {
            parentId = hrBillInvoiceDTO.getParentId() == null ? hrBillInvoiceDTO.getId() : hrBillInvoiceDTO.getParentId();
        }
        invoiceDTO.setId(RandomUtil.generateId()).setParentId(parentId)
            .setApplyDate(LocalDate.now()).setCreatedDate(LocalDateTime.now()).setLastModifiedDate(LocalDateTime.now())
            .setInvoiceState(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())
        ;
        List<HrBillInvoiceRecordDTO> hrBillInvoiceRecordDTOS = choiceInvoiceRecords.stream().filter(lst -> lst.getCheckOn() != null && lst.getCheckOn() == 1).collect(Collectors.toList());
        double totalAmount = hrBillInvoiceRecordDTOS.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
        double taxAmount = hrBillInvoiceRecordDTOS.stream().collect(Collectors.summarizingDouble(value -> value.getTaxAmount())).getSum();
        invoiceDTO.setTotalAmount(totalAmount)
            .setTaxAmount(taxAmount)
            .setTotalAmountCn(Convert.digitToChinese(totalAmount))
            .setApplyDate(LocalDate.now());
        invoiceDTO.setCreatedDate(LocalDateTime.now());
        invoiceDTO.setLastModifiedDate(LocalDateTime.now());
        HrBillInvoice billInvoice = hrBillInvoiceMapper.toEntity(invoiceDTO);
        this.hrBillInvoiceRepository.insert(billInvoice);
        //添加开票结算单中间表
        this.createHrBillInvoiceReview(billInvoice.getId(), hrBillInvoiceDTO.getFeeReviewId());
        if (invoiceDTO.getFileIds() != null) {
            invoiceDTO.getFileIds().forEach(fileId -> {
                hrAppendixService.saveAppendixUnion(fileId, invoiceDTO.getId());
            });
        }
        for (HrBillInvoiceRecordDTO invoiceRecordDTO : choiceInvoiceRecords) {
            HrBillInvoiceRecord oldRecord = hrBillInvoiceRecordMapper.toEntity(invoiceRecordDTO);
            if (invoiceRecordDTO.getCheckOn() != null && invoiceRecordDTO.getCheckOn() == 1) {
                HrBillInvoiceRecord newRecord = hrBillInvoiceRecordMapper.toEntity(invoiceRecordDTO);
                newRecord.setId(null)
                    .setInvoiceId(billInvoice.getId())
                    .setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                newRecord.setCreatedDate(LocalDateTime.now());
                hrBillInvoiceRecordService.save(newRecord);
                oldRecord.setLevelId(newRecord.getId()).setState(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                if (flag && hrBillInvoiceDTO.getParentId() != null) {
                    HrBillInvoiceRecord invoiceRecord = hrBillInvoiceRecordService.getOne(new QueryWrapper<HrBillInvoiceRecord>()
                        .eq("invoice_id", hrBillInvoiceDTO.getParentId()).eq("level_id", oldRecord.getId())
                        .orderByDesc("created_date").last("LIMIT 1"));
                    if (invoiceRecord != null) {
                        invoiceRecord.setLevelId(newRecord.getId())
                            .setContent(newRecord.getContent())
                            .setTotalAmount(newRecord.getTotalAmount())
                            .setTaxRate(newRecord.getTaxRate())
                            .setTaxAmount(newRecord.getTaxAmount())
                            .setNoTaxAmount(newRecord.getNoTaxAmount())
                            .setState(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                        hrBillInvoiceRecordService.updateById(invoiceRecord);
                    }
                }
            }
            oldRecord.setLastModifiedDate(LocalDateTime.now());
            hrBillInvoiceRecordService.updateById(oldRecord);
        }
        // 如果发起了申请则保存日志
        if (BillInvoiceApproveEnums.ON_CUSTOMER_MANAGER.getKey().equals(billInvoice.getApproveStatus())) {
            applyOpLogsService.saveHrApplyOpLogsPhaseTWO(billInvoice.getId(), null, billInvoice.getApplyId(),
                "发起开票申请 ", null, ServiceCenterEnum.INVOICE_APPLY.getKey());
        }

    }

    /**
     * 修改开票申请
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    @Override
    public Optional<HrBillInvoiceDTO> updateHrBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO) {
        return Optional.ofNullable(this.hrBillInvoiceRepository.selectById(hrBillInvoiceDTO.getId()))
            .map(roleTemp -> {
                // 保存账单明细
                List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceDTO.getInvoiceRecords();
                if (invoiceRecords == null || invoiceRecords.size() == 0) {
                    throw new CommonException("账单明细不能为空！");
                }
                // 先删除关联的明细表数据和关联的附件数据
                if (roleTemp.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())) {
                    List<String> collect = invoiceRecords.stream().filter(lst -> lst.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey()) ||
                            lst.getState().equals(BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey()))
                        .map(HrBillInvoiceRecordDTO::getTitle).collect(Collectors.toList());
                    if (hrBillInvoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.NOT_LAUNCH.getKey())) {
                        //审核未通过基础上暂存状态不改变
                        hrBillInvoiceDTO.setApproveStatus(roleTemp.getApproveStatus());
                    }
                    if (CollectionUtils.isNotEmpty(collect)) {
                        throw new CommonException("开票明细中[ " + String.join(",", collect) + " ]已重新发起开票申请，不可再次发起！");
                    }
                    //重新发起开票明细和上级可开发票明细状态修改为重新锁定
                    List<String> invoiceRecordIds = invoiceRecords.stream().map(HrBillInvoiceRecordDTO::getId).collect(Collectors.toList());
                    //获取上级可开发票明细
                    if (roleTemp.getParentId() != null) {
                        this.selectBillInvoiceRecord(roleTemp.getParentId(), invoiceRecordIds);
                    }
                    if (CollectionUtils.isNotEmpty(invoiceRecordIds)) {
                        hrBillInvoiceRecordService.updateState(invoiceRecordIds, BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey());
                    }
                    if (roleTemp.getParentId() != null) {
                        this.selectBillInvoiceRecord(roleTemp.getParentId(), invoiceRecordIds);
                    }
                    if (roleTemp.getParentId() != null) {
                        QueryWrapper<HrBillInvoiceRecord> qw = new QueryWrapper<>();
                        qw.eq("invoice_id", roleTemp.getParentId());
                        qw.in("state", BillInvoiceApproveEnums.InvoiceRecordState.NOT_LOCKED.getKey(), BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey());
                        List<HrBillInvoiceRecord> recordList = hrBillInvoiceRecordService.list(qw);
                        Integer lockState;
                        if (CollectionUtils.isEmpty(recordList)) {
                            lockState = BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey();
                        } else {
                            lockState = BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey();
                        }
                        hrBillInvoiceRepository.updateInvoiceLockState(roleTemp.getParentId(), lockState);
                    }
                    /*if (checkOnRecordList.size() != hrBillInvoiceDTO.getInvoiceRecords().size()){//从审核未通过的明细选择某些数据重新发起一条开票记录
                        this.saveInvoiceRecord(hrBillInvoiceDTO, hrBillInvoiceDTO.getInvoiceRecords(),true);
                        return hrBillInvoiceDTO;
                    }*/
                    // 保存审批记录
                    applyOpLogsService.saveHrApplyOpLogsPhaseTWO(roleTemp.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
                        "重新发起开票申请 ", null, ServiceCenterEnum.INVOICE_APPLY.getKey());
                }
                hrAppendixService.deleteByUnionId(hrBillInvoiceDTO.getId());
                // 保存附件关联
                if (hrBillInvoiceDTO.getFileIds() != null) {
                    hrBillInvoiceDTO.getFileIds().forEach(fileId -> {
                        hrAppendixService.saveAppendixUnion(fileId, hrBillInvoiceDTO.getId());
                    });
                }
                //新增开票暂存后发送明细只有选中的明细
                if ((roleTemp.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey()) || roleTemp.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.CUSTOMIZE.getKey()))
                    && roleTemp.getApproveStatus().equals(BillInvoiceApproveEnums.NOT_LAUNCH.getKey())) {
                    hrBillInvoiceRecordService.deleteByInvoiceId(roleTemp.getId());
                    List<HrBillInvoiceRecord> list = new ArrayList<>();
                    invoiceRecords.forEach(invoiceRecordDTO -> {
                        HrBillInvoiceRecord invoiceRecord = hrBillInvoiceRecordMapper.toEntity(invoiceRecordDTO);
                        invoiceRecord.setId(RandomUtil.generateId()).setInvoiceId(roleTemp.getId());
                        list.add(invoiceRecord);
                    });
                    hrBillInvoiceRecordService.saveBatch(list);
                } else {
                    for (HrBillInvoiceRecordDTO invoiceRecord : invoiceRecords) {
                        HrBillInvoiceRecord entity = hrBillInvoiceRecordMapper.toEntity(invoiceRecord);
                        hrBillInvoiceRecordService.updateById(entity);
                    }
                }
                if (hrBillInvoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.NOT_LAUNCH.getKey())) {//暂存功能是只修改数据不会走流程结点
                    hrBillInvoiceDTO.setApproveStatus(null);
                    hrBillInvoiceDTO.setTotalAmount(null);
                    hrBillInvoiceDTO.setTotalAmountCn(null);
                    hrBillInvoiceDTO.setTaxAmount(null);
                }
                HrBillInvoice hrBillInvoice = this.hrBillInvoiceMapper.toEntity(hrBillInvoiceDTO);
                this.hrBillInvoiceRepository.updateById(hrBillInvoice);
                log.info("Update HrBillInvoice:{}", hrBillInvoiceDTO);
                return hrBillInvoiceDTO;
            });
    }

    /**
     * 获取上级可开发票明细
     *
     * @param parentId
     * @param invoiceRecordIds
     */
    private void selectBillInvoiceRecord(String parentId, List<String> invoiceRecordIds) {
        QueryWrapper<HrBillInvoiceRecord> qw = new QueryWrapper<>();
        qw.eq("invoice_id", parentId);
        qw.in("level_id", invoiceRecordIds);
        List<HrBillInvoiceRecord> hrBillInvoiceRecords = hrBillInvoiceRecordService.list(qw);
        if (CollectionUtils.isNotEmpty(hrBillInvoiceRecords)) {
            List<String> ids = hrBillInvoiceRecords.stream().map(HrBillInvoiceRecord::getId).collect(Collectors.toList());
            invoiceRecordIds.addAll(ids);
        }
    }

    /**
     * 保存发票明细和附件
     *
     * @param hrBillInvoiceDTO
     */
    private void saveRecordAndFile(HrBillInvoiceDTO hrBillInvoiceDTO) {
        // 保存附件关联
        if (hrBillInvoiceDTO.getFileIds() != null) {
            hrBillInvoiceDTO.getFileIds().forEach(fileId -> {
                hrAppendixService.saveAppendixUnion(fileId, hrBillInvoiceDTO.getId());
            });
        }

        // 保存账单明细
        if (hrBillInvoiceDTO.getInvoiceRecords() == null || hrBillInvoiceDTO.getInvoiceRecords().size() == 0) {
            throw new CommonException("账单明细不能为空！");
        }
        List<HrBillInvoiceRecord> list = new ArrayList<>();
        Set<String> feeReviewIds = new HashSet<>();
        hrBillInvoiceDTO.getInvoiceRecords().forEach(invoiceRecordDTO -> {
            invoiceRecordDTO.setInvoiceId(hrBillInvoiceDTO.getId());
            list.add(hrBillInvoiceRecordMapper.toEntity(invoiceRecordDTO));
            List<String> reviewIds = invoiceRecordDTO.getFeeReviewIds();
            if (reviewIds != null && !reviewIds.isEmpty()) {
                feeReviewIds.addAll(reviewIds);
            }
        });
        hrBillInvoiceRecordService.saveBatch(list);
        if (!feeReviewIds.isEmpty()) {
            feeReviewIds.forEach(reviewId -> {
                //添加开票账单中间表
                this.createHrBillInvoiceReview(hrBillInvoiceDTO.getId(), reviewId);
            });
        }

    }

    /**
     * 添加开票账单中间表
     *
     * @param invoiceId 开票ID
     * @param reviewId  结算单ID
     */
    private void createHrBillInvoiceReview(String invoiceId, String reviewId) {
        HrBillInvoiceReview invoiceReview = new HrBillInvoiceReview();
        invoiceReview.setInvoiceId(invoiceId);
        invoiceReview.setFeeReviewId(reviewId);
        hrBillInvoiceReviewRepository.insert(invoiceReview);
    }

    /**
     * 查询开票申请详情
     *
     * @param id
     * @return
     */
    @Override
    public HrBillInvoiceDTO getHrBillInvoice(String id) {
        log.info("Get HrBillInvoice :{}", id);

        HrBillInvoiceDTO hrBillInvoiceDTO = this.hrBillInvoiceRepository.getHrBillInvoice(id);

        // 查询发票明细列表
        List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceRecordService.getByInvoiceId(id);
        hrBillInvoiceDTO.setInvoiceRecords(invoiceRecords);

        // 如果通知人不为空，则设置人
        if (hrBillInvoiceDTO.getNoticeRoles() != null) {
            List<String> roleIds = Arrays.asList(hrBillInvoiceDTO.getNoticeRoles().split(","));
            String noticeUsers = roleService.getRolesByIds(roleIds);
            hrBillInvoiceDTO.setNoticeUsers(noticeUsers);
        }

        List<HrAppendixDTO> appendixes = hrAppendixService.getByUnionId(id);
        hrBillInvoiceDTO.setAppendixes(appendixes);

        // 如果进入审批了，则查询一下审批的记录
        List<HrApplyOpLogsDTO> list = applyOpLogsService.getByApplyId(id);
        hrBillInvoiceDTO.setOpLogs(list);

        // 如果已经上传发票了，则需要查询发票附件
        if (hrBillInvoiceDTO.getInvoiceFiledIds() != null) {
            List<HrAppendixDTO> invoiceAppendixes = hrAppendixService.getHrAppendixListByIds(Arrays.asList(hrBillInvoiceDTO.getInvoiceFiledIds().split(",")));
            hrBillInvoiceDTO.setInvoiceAppendixes(invoiceAppendixes);
        }
        return hrBillInvoiceDTO;
    }

    /**
     * 删除开票申请
     *
     * @param id
     */
    @Override
    public void deleteHrBillInvoice(String id) {
        Optional.ofNullable(this.hrBillInvoiceRepository.selectById(id))
            .ifPresent(hrBillInvoice -> {
                this.hrBillInvoiceRepository.deleteById(id);
                log.info("Delete HrBillInvoice:{}", hrBillInvoice);
            });
    }

    /**
     * 批量删除开票申请
     *
     * @param ids
     */
    @Override
    public void deleteHrBillInvoice(List<String> ids) {
        log.info("Delete HrBillInvoices:{}", ids);
        if (ids != null) {
            for (String id : ids) {
                HrBillInvoice billInvoice = getById(id);
                if (billInvoice == null) {
                    throw new CommonException("无效的id");
                }
                //可开发票数据是流程自动化数据不可删除
                if (billInvoice.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())) {
                    throw new CommonException("不可删除流程自动化开票数据！");
                }
                //isDefault == 0是流程自动化数据只能删除审核未通过、已作废的数据
                if (billInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.PROCESS_AUTOMATION.getKey())
                    && !billInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())
                    && !billInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.ALREADY_CANCEL.getKey())) {
                    throw new CommonException("只能删除[审核拒绝、已作废]的数据！");
                }
                //isDefault == 1是新建数据只能删除暂存的数据
                if (billInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey())
                    && !billInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.NOT_LAUNCH.getKey())
                    && !billInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())
                    && !billInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.ALREADY_CANCEL.getKey())
                ) {
                    throw new CommonException("只能删除[审核拒绝、已作废]的数据！");
                }
                this.hrBillInvoiceRepository.deleteById(id);
                //删除开票明细
                hrBillInvoiceRecordService.deleteByInvoiceId(id);
                //删除开票结算单中间表
                hrBillInvoiceReviewRepository.delByInvoiceId(id);
            }
            this.sysOperLogService.insertSysOperLog(ModuleTypeEnum.BILL_INVOICE.getValue(), BusinessTypeEnum.DELETE.getKey(), JSON.toJSONString(ids), null, null, null, null, null, null);
        }
    }

    /**
     * 分页查询开票申请
     *
     * @param hrBillInvoiceDTO
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public IPage findPage(HrBillInvoiceDTO hrBillInvoiceDTO, Long pageNumber, Long pageSize) {
        Page<HrBillInvoice> page = new Page<>(pageNumber, pageSize);

        List<String> clientIds = hrClientService.selectClientIdByUserId();

        if (hrBillInvoiceDTO.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.INVOICE_RECORD.getKey())) {
            JWTUserDTO curUser = SecurityUtils.getJwtUser();
            hrBillInvoiceDTO.setCurUserId(curUser.getId());

            // 获取当前用户的可审批状态
            BillInvoiceApproveEnums approveEnums = EnumUtils.getEnum(BillInvoiceApproveEnums.class, "getRoleKey", curUser.getCurrentRoleKey());
            if (approveEnums != null) {
                hrBillInvoiceDTO.setCurUserStartStatus(approveEnums.getKey());
            }
        }
        IPage<HrBillInvoiceDTO> iPage = this.hrBillInvoiceRepository.findPage(page, hrBillInvoiceDTO, clientIds);
        return iPage;
    }

    /**
     * 审批
     *
     * @param hrBillInvoiceApproveDTO
     */
    @Override
    public void approve(HrBillInvoiceApproveDTO hrBillInvoiceApproveDTO) {
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();

        List<HrBillInvoice> hrBillInvoices = listByIds(hrBillInvoiceApproveDTO.getIds());
        for (HrBillInvoice hrBillInvoice : hrBillInvoices) {
            BillInvoiceApproveEnums approveEnums = EnumUtils.getEnumByKey(BillInvoiceApproveEnums.class, hrBillInvoice.getApproveStatus());
            boolean checkAuth = false;
            switch (approveEnums) {
                case SUCCESS:
                case REJECT:
                    throw new CommonException("审核流程已结束,操作异常!");
                case ON_CUSTOMER_MANAGER:
                    // 待客服经理审批
                    checkAuth = UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey().equals(userDTO.getCurrentRoleKey());
                    hrBillInvoice.setApproveStatus(BillInvoiceApproveEnums.ON_ACCOUNTING.getKey());
                    break;
                case ON_ACCOUNTING:
                    // 待会计审批
                    checkAuth = UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey());
                    hrBillInvoice.setApproveStatus(BillInvoiceApproveEnums.SUCCESS.getKey());
                    break;
//            case ON_FINANCIAL_DIRECTOR:
//                // 待总经理审批
//                checkAuth = UserRoleTypeEnum.TOTAL_MANAGER.getKey().equals(userDTO.getCurrentRoleKey());
//                break;
                default:
                    break;
            }
            if (!checkAuth) {
                throw new CommonException("当前流程为[" + approveEnums.getName() + "],您没有审核权限!");
            }

            String reason = StringUtils.isEmpty(hrBillInvoiceApproveDTO.getReason()) ? "" : hrBillInvoiceApproveDTO.getReason();
            List<HrBillInvoiceRecord> invoiceRecordList = hrBillInvoiceRecordService.list(new QueryWrapper<HrBillInvoiceRecord>().eq("invoice_id", hrBillInvoice.getId()));
            if (hrBillInvoiceApproveDTO.getApproveType() == 0) {
                if (StringUtils.isBlank(hrBillInvoiceApproveDTO.getReason())) {
                    throw new CommonException("拒绝理由不能为空");
                }
                // 设置为不通过
                hrBillInvoice.setApproveStatus(BillInvoiceApproveEnums.REJECT.getKey());
                List<String> invoiceRecordIds = invoiceRecordList.stream().map(HrBillInvoiceRecord::getId).collect(Collectors.toList());//审核未通过开票明细
                //获取上级可开发票明细
                if (hrBillInvoice.getParentId() != null) {
                    hrBillInvoiceRepository.updateInvoiceLockState(hrBillInvoice.getParentId(), BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
                    this.selectBillInvoiceRecord(hrBillInvoice.getParentId(), invoiceRecordIds);
                }
                hrBillInvoiceRecordService.updateState(invoiceRecordIds, BillInvoiceApproveEnums.InvoiceRecordState.UNLOCK.getKey());
                saveOrUpdate(hrBillInvoice);
                // 发送站内信
                this.sendMsg(hrBillInvoice, userDTO);

                // 保存审批记录
                applyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrBillInvoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
                    "审核拒绝! " + reason, null, ServiceCenterEnum.INVOICE_APPLY.getKey());
                continue;
            }
            saveOrUpdate(hrBillInvoice);
            if (hrBillInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.SUCCESS.getKey())) {
                if (hrBillInvoice.getParentId() != null) {
                    //查询上级可开发票是否锁定，如果锁定，查看开票明细对应的记录是否全部审核通过，如果全部通过则隐藏可开发票数据
                    List<HrBillInvoiceRecordDTO> invoiceRecordDTOList = hrBillInvoiceRecordService.getByInvoiceId(hrBillInvoice.getParentId());
                    if (invoiceRecordDTOList.get(0).getInvoiceLockState().equals(BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey())) {
                        List<String> invoiceRecordIds = invoiceRecordDTOList.stream().map(HrBillInvoiceRecordDTO::getLevelId).collect(Collectors.toList());
                        List<HrBillInvoiceRecordDTO> billInvoiceRecordDTOS = hrBillInvoiceRecordService.getByIdBatch(invoiceRecordIds);
                        List<HrBillInvoiceRecordDTO> list = billInvoiceRecordDTOS.stream().filter(lst -> !lst.getApproveStatus().equals(BillInvoiceApproveEnums.SUCCESS.getKey())).collect(Collectors.toList());
                        if (list == null || list.isEmpty()) {
                            hrBillInvoice.setIsShow(1);
                            hrBillInvoiceRepository.updateBeforeInvoice(Collections.singletonList(hrBillInvoice.getParentId()), BillInvoiceApproveEnums.InvoiceLockState.LOCKED.getKey(), 1);
                        }
                    }
                }
                HrClient hrClient = hrClientService.getById(hrBillInvoice.getClientId());
                //结算单信息添加到到账记录以及报销申请
                if (hrBillInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.PROCESS_AUTOMATION.getKey())) {
                    List<HrFeeReview> hrFeeReviewList = hrFeeReviewRepository.findFeeReviewByInvoiceId(hrBillInvoice.getId());
                    if (hrFeeReviewList.isEmpty()) {
                        throw new CommonException("未查询到对应的结算单信息！");
                    }
                    HrFeeReview hrFeeReview = hrFeeReviewList.get(0);
                    HrClient rootParentClient = hrClientRepository.getRootParentClient(hrFeeReview.getClientId());
                    HrBillTotal hrBillTotal = hrBillTotalRepository.selectOne(new QueryWrapper<HrBillTotal>().eq("bill_id", hrFeeReview.getId()).last("LIMIT 1"));
                    List<HrBill> hrBills = hrBillRepository.selectBatchIds(Arrays.asList(hrFeeReview.getBillId().split(",")));
                    /*boolean flag = true;
                    if (hrBills.get(0).getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())){
                        flag = false;
                    }*/
                    this.saveArrivalAndReimbursement(hrFeeReview, hrBillTotal, hrClient, hrBills, 1, invoiceRecordList, rootParentClient);
                } else {//新建发起开票 和  自定义开票
                    this.createArrivalAndReimbursement(hrBillInvoice, invoiceRecordList, hrClient);
                }
            }
            // 保存审批记录
            applyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrBillInvoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
                "审核通过! " + reason, null, ServiceCenterEnum.INVOICE_APPLY.getKey());
        }
    }

    /**
     * 创建到账记录和报销申请
     *
     * @param hrBillInvoice
     * @param invoiceRecordList
     * @param hrClient
     */
    private void createArrivalAndReimbursement(HrBillInvoice hrBillInvoice, List<HrBillInvoiceRecord> invoiceRecordList, HrClient hrClient) {
        //同一可开发票发起的开票记录，有一个审核通过将全部金额添加到
        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        HrArrivalRecordDTO hrArrivalRecordDTO = new HrArrivalRecordDTO();
        hrArrivalRecordDTO.setArrivalDate(LocalDate.now())
            .setClientId(hrBillInvoice.getClientId())
            .setBillInvoiceId(hrBillInvoice.getId())
            .setPayYear(year)
            .setPayMonthly(month)
            .setReceivableAmount(hrBillInvoice.getTotalAmount() == null ? BigDecimal.ZERO : new BigDecimal(hrBillInvoice.getTotalAmount()));
        hrArrivalRecordService.createHrArrivalRecord(hrArrivalRecordDTO);

        HrBillReimbursementApplyDTO result = new HrBillReimbursementApplyDTO();
        result
            .setClientId(hrBillInvoice.getClientId())
            .setClientName(hrClient.getClientName())
            .setTitle(hrClient.getClientName() + (month > 9 ? "" : "0") + month + "月份资金发放申请")
            .setPayYear(year)
            .setPayMonth(month)
            .setApproveStatus(BillReimbApproveEnums.NOT_LAUNCH.getKey())
            .setApplyDate(LocalDate.now())
            .setFlag(false)
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())
            .setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());

        List<HrBillReimbursementApplyDetailDTO> detailDTOList = new ArrayList<>();
        List<HrBillInvoiceRecord> collect = invoiceRecordList.stream().filter(lst -> lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            double wageIncome = collect.stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
            detailDTOList.add(new HrBillReimbursementApplyDetailDTO().setInvoiceType(BillReimbApproveEnums.InvoiceTypeEnum.DF_SALARY.getKey()).setAmount(new BigDecimal(wageIncome)));
        }
        List<HrBillInvoiceRecord> collect1 = invoiceRecordList.stream().filter(lst -> lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect1)) {
            double socialSecurityIncome = collect1.stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
            detailDTOList.add(new HrBillReimbursementApplyDetailDTO().setInvoiceType(BillReimbApproveEnums.InvoiceTypeEnum.DF_SOCIAL_SECURITY.getKey()).setAmount(new BigDecimal(socialSecurityIncome)));
        }
        List<HrBillInvoiceRecord> collect2 = invoiceRecordList.stream().filter(lst -> lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect2)) {
            double accumulationFundIncome = collect2.stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
            detailDTOList.add(new HrBillReimbursementApplyDetailDTO().setInvoiceType(BillReimbApproveEnums.InvoiceTypeEnum.DF_ACCUMULATION_FOUND.getKey()).setAmount(new BigDecimal(accumulationFundIncome)));
        }
        List<HrBillInvoiceRecord> collect3 = invoiceRecordList.stream().filter(lst -> !lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey())
            && !lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getKey())
            && !lst.getContent().equals(BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect3)) {
            double otherIncome = collect3.stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
            detailDTOList.add(new HrBillReimbursementApplyDetailDTO().setInvoiceType(BillReimbApproveEnums.InvoiceTypeEnum.DF_OTHER.getKey()).setAmount(new BigDecimal(otherIncome)));
        }
        result.setDetailDTOList(detailDTOList);
        BigDecimal amount = detailDTOList.stream().map(HrBillReimbursementApplyDetailDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setAmount(amount);
        if (BigDecimalCompare.of(amount).eq(BigDecimal.ZERO)) {
            result.setIsShow(1);
        }
        hrBillReimbursementApplyService.createHrBillReimbursementApply(result);
    }

    /**
     * 发送站内信
     *
     * @param hrBillInvoice
     * @param userDTO
     */
    private void sendMsg(HrBillInvoice hrBillInvoice, JWTUserDTO userDTO) {
        if (StringUtils.isNotEmpty(hrBillInvoice.getNoticeRoles())) {
            String resultMsg = BillInvoiceApproveEnums.SUCCESS.getKey().equals(hrBillInvoice.getApproveStatus()) ? "审核成功!" : "审核失败!";
            HrBillInvoiceDTO billInvoiceDTO = this.getHrBillInvoice(hrBillInvoice.getId());
            HrMessageListDTO messageListDTO = new HrMessageListDTO()
                .setCreatedById(userDTO.getId())
                .setTitle("开票申请【" + billInvoiceDTO.getApplyName() + "-" + billInvoiceDTO.getClientName() + "】" + resultMsg)
                .setContent(PcMessageContentEnum.MESSAGE_BILL_INVOICE_APPLY.getKey())
                .setContentType(2)
                .setRoleList(Arrays.asList(hrBillInvoice.getNoticeRoles().split(",")));
            this.hrMessageListService.createHrMessageList(messageListDTO);
        }
    }

    /**
     * 开票
     *
     * @param billInvoiceKpDTO
     */
    @Override
    public String kp(HrBillInvoiceKpDTO billInvoiceKpDTO) {
        HrBillInvoice hrBillInvoice = getById(billInvoiceKpDTO.getId());
        hrBillInvoice.setInvoiceFiledIds(StringUtils.join(billInvoiceKpDTO.getIds(), ","));
        hrBillInvoice.setInvoiceRemark(billInvoiceKpDTO.getInvoiceRemark());

        // 设置为已开票
        hrBillInvoice.setInvoiceStatus(1);
        saveOrUpdate(hrBillInvoice);
        List<HrFeeReview> hrFeeReviewList = hrFeeReviewRepository.findFeeReviewByInvoiceId(hrBillInvoice.getId());
        if (!hrFeeReviewList.isEmpty()) {
            HrSeals hrSeals = hrSealsRepository.selectOne(new QueryWrapper<HrSeals>().eq("is_delete", 0).eq("seal_name", "财务专用章")
                .orderByDesc("created_date").last("LIMIT 1"));
            if (hrSeals == null || StringUtils.isBlank(hrSeals.getSignId()) || StringUtils.isBlank(hrSeals.getSealUrl())) {
                throw new CommonException("没找到可用的财务章！");
            }
            //判断是否中石化账单
            List<String> billIds = Arrays.asList(hrFeeReviewList.get(0).getBillId().split(","));
            HrBill hrBill = hrBillRepository.selectOne(new QueryWrapper<HrBill>().in("id", billIds).orderByDesc("created_date").last("LIMIT 1"));

            for (HrFeeReview hrFeeReview : hrFeeReviewList) {
                if (hrFeeReview.getDetailAppendixId() != null && hrFeeReview.getDetailPdfAppendixId() == null) {
                    HrAppendixDTO hrAppendixDTO = hrAppendixService.getHrAppendix(hrFeeReview.getDetailAppendixId());
                    try {
                        int x = 520;
                        int y = 480;
                        if (hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey())) {
                            x = 495;
                            y = 460;
                        }
                        String pdfFileUrl = this.excelToPdf(hrFeeReview, hrAppendixDTO, hrBill);
                        HrAppendix uploadTempPdf = hrAppendixService.uploadTempPdf(pdfFileUrl);
                        String contractNum = RandomUtil.generateNo();
                        this.eCloudComponent.uploadContractUrl(contractNum, uploadTempPdf.getOriginName(), uploadTempPdf.getFileUrl());
                        this.eCloudComponent.electronicBillSignature(contractNum, uploadTempPdf.getFileUrl(), hrSeals.getSignId(), x, y);
                        HrAppendix hrAppendix = this.eCloudComponent.downloadContract(contractNum, CompanyInfoEnum.FIRST_PART_PHONE.getValue());
                        hrFeeReview.setContractNum(contractNum);
                        hrFeeReview.setDetailPdfAppendixId(hrAppendix.getId());
                        hrFeeReviewRepository.updateById(hrFeeReview);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new CommonException("EXCEL转PDF异常！异常提示【" + e.getMessage() + "】");
                    }
                }
            }
        }
        // TODO Jigsaw - 2025/8/11 ：财务帐号自动生成凭证

        HrBillInvoiceDTO hrBillInvoiceDTO = new HrBillInvoiceDTO();
        BeanUtils.copyProperties(hrBillInvoice, hrBillInvoiceDTO);
        hrBillInvoiceDTO.setAttachment("1");
        hrBillInvoiceDTO.setPrepareddate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        // 用于设置会计期间，使用当前月份
        hrBillInvoiceDTO.setPayMonthly(LocalDate.now().getMonthValue());
        String message = "开票成功";
        try {
            createVoucherNcc(hrBillInvoiceDTO);
        } catch (Exception e) {
            message = "生成凭证失败，失败原因：" + e.getMessage();
            e.printStackTrace();
        }

        // 添加操作日志
        applyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrBillInvoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
            "开票成功!", null, ServiceCenterEnum.INVOICE_APPLY.getKey());
        return message;
    }

    /**
     * excel转pdf
     *
     * @param hrFeeReview   结算单信息
     * @param hrAppendixDTO excel附件
     * @param hrBill        账单信息
     * @return PDF路径
     */
    @Override
    public String excelToPdf(HrFeeReview hrFeeReview, HrAppendixDTO hrAppendixDTO, HrBill hrBill) throws Exception {
        //excel文件路径
        String excelFile = tempPath + hrFeeReview.getId() + ".xlsx";
        FileUtil.saveUrlAs(hrAppendixDTO.getFileUrl(), excelFile);
        //pdf文件输出路径
        String pdfName = tempPath + hrFeeReview.getId() + ".pdf";
        // 使用语言包字体
        BaseFont abf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        /*if (System.getProperty("os.name").contains("Window")) {
            try {
                abf = BaseFont.createFont("C:/Windows/Fonts/simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (com.itextpdf.text.DocumentException e) {
                e.printStackTrace();
            }
        } else if (System.getProperty("os.name").contains("Mac")) {
            try {
                abf = BaseFont.createFont("/Users/<USER>/Downloads/tmp/simsun.ttc,0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (com.itextpdf.text.DocumentException e) {
                e.printStackTrace();
            }
        } else {
            try {
                abf = BaseFont.createFont(fontPath + ",0", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (com.itextpdf.text.DocumentException e) {
                e.printStackTrace();
            }
        }*/
        //字体
        com.itextpdf.text.Font font = new com.itextpdf.text.Font(abf, 6);

        //读取excel
        FileInputStream inputStream = new FileInputStream(new File(excelFile));
        org.apache.poi.ss.usermodel.Workbook work = getWorkbook(inputStream, excelFile);
        Sheet sheet = work.getSheetAt(0);

        //获取excel总列数
        int totalCol = sheet.getRow(0).getPhysicalNumberOfCells();
        //准备遍历excel
        Iterator<Row> rowIterator = sheet.iterator();

        //生成一个pdf
        Rectangle rectangle = new Rectangle(PageSize.A4);
        rectangle = rectangle.rotate();
        Document document = new Document(rectangle, 0.5F, 0.5F, 20, 1);
        PdfWriter.getInstance(document, new FileOutputStream(pdfName));
        document.open();

        //在pdf中创建一个表格
        PdfPTable table = new PdfPTable(totalCol);
        table.setKeepTogether(false);
        table.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.getDefaultCell().setBorder(1);
        table.setSplitLate(true);
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        //遍历excel， 将数据输出到pdf的表格中
        PdfPCell tableCell = null;
        Map<String, String> hashMap = new HashMap<>();
        int num = 2;
        int type = hrBill.getBillType().equals(BillEnum.BillType.SINOPEC_EXPENSE_BILL.getKey()) ? 1 : 0;
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String value = cellToStringValue(row.getCell(0));
            if (value.contains("费用明细")) {
                num = 4;
            }
            if (type == 1) {
                num = 5;
            }
            String value1 = "";
            Row lowerRow = sheet.getRow(row.getRowNum() + 1);
            if (lowerRow != null) {
                value1 = cellToStringValue(lowerRow.getCell(0));
            }
            Iterator<Cell> cellIterator = row.cellIterator();
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                String cellValue = "";
                int rowNum = 1;
                int colNum = 1;
                int i = cell.getRowIndex();
                int j = cell.getColumnIndex();
                cellValue = cellToStringValue(cell);
                boolean flag = false;
                //处理表头合并
                for (CellRangeAddress range : mergedRegions) {
                    if (j >= range.getFirstColumn() && j <= range.getLastColumn() && i >= range.getFirstRow() && i <= range.getLastRow()) {
                        rowNum = range.getLastRow() - range.getFirstRow() + 1;
                        colNum = range.getLastColumn() - range.getFirstColumn() + 1;
                    }
                }
                if (type == 1) {//中石化账单处理
                    if (i == 1) {
                        break;
                    }
                    if (StringUtils.isEmpty(cellValue)) {
                        continue;
                    }
                    if (i < num) {
                        flag = handleMergeCell(sheet, hashMap, cellValue, value1, i, j);
                    }
                } else {
                    if (StringUtils.isEmpty(cellValue) && !value.equals("合计")) {
                        if (i > num - 1) {
                            cellValue = "0";
                        } else {
                            continue;
                        }
                    }
                    if (num != 2) {//处理标题以及合计
                        if (i == 1) {
                            break;
                        }
                        if (cellValue.equals("0") && i == 0) {
                            break;
                        }
                        if (value.equals("合计")) {
                            if (j > 0 && j < colNum && (StringUtils.isEmpty(cellValue) || cellValue.equals("NaN") || Integer.parseInt(cellValue) == 0)) {
                                continue;
                            }
                        }
                    }
                    if (i < num) {
                        flag = handleMergeCell(sheet, hashMap, cellValue, value1, i, j);
                    }
                }
                if (flag) {
                    continue;
                }
                if (value.equals("费用明细") || cellValue.contains("费用统计表")) {
                    tableCell = new PdfPCell(new Phrase(cellValue, new com.itextpdf.text.Font(abf, 11, Font.BOLD)));
                    tableCell.setFixedHeight(35);
                    tableCell.disableBorderSide(15);
                } else {
                    if (value.equals("合计")) {
                        try {
                            BigDecimal bigDecimal = new BigDecimal(cellValue).setScale(2, BigDecimal.ROUND_HALF_UP);
                            tableCell = new PdfPCell(new Phrase(bigDecimal.toString(), font));
                        } catch (Exception e) {
                            tableCell = new PdfPCell(new Phrase(cellValue, font));
                        }
                    } else {
                        tableCell = new PdfPCell(new Phrase(cellValue, font));
                    }
                }
                tableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                tableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                tableCell.setRowspan(rowNum);
                tableCell.setColspan(colNum);
                table.addCell(tableCell);
                if (cellValue.contains("费用明细") || cellValue.contains("费用统计表")) {
                    break;
                }
            }
        }
        table.setWidthPercentage(96);
        table.setLockedWidth(false);
        table.setHeaderRows(num);
        document.add(table);
        document.close();
        inputStream.close();
        //删除excel临时文件
        FileUtil.deleteTempFile(excelFile);
        return pdfName;
    }

    private String cellToStringValue(Cell rowCell) {
        if (rowCell == null) {
            return "";
        }
        if (rowCell.getCellType() == CellType.NUMERIC) {
            rowCell.setCellType(CellType.STRING);
        }
        return rowCell.getStringCellValue();
    }

    /**
     * 创建到账记录以及报销申请
     *
     * @param roleTemp          结算单信息
     * @param hrBillTotal       结算单汇总
     * @param hrClient          客户信息
     * @param hrBills           账单
     * @param integer           0结算单审核 1开票单审核
     * @param invoiceRecordList 开票明细
     * @param rootParentClient  顶级客户
     */
    @Override
    public void saveArrivalAndReimbursement(HrFeeReview roleTemp, HrBillTotal hrBillTotal, HrClient hrClient, List<HrBill> hrBills, Integer integer, List<HrBillInvoiceRecord> invoiceRecordList, HrClient rootParentClient) {
        HrArrivalRecordDTO hrArrivalRecordDTO = new HrArrivalRecordDTO();
        hrArrivalRecordDTO.setArrivalDate(LocalDate.now())
            .setClientId(roleTemp.getClientId())
            .setPayYear(roleTemp.getPayYear())
            .setPayMonthly(roleTemp.getPayMonthly())
            .setIsDefault(2);
        if (integer == 0) {
            hrArrivalRecordDTO.setFeeReviewId(roleTemp.getId());
            hrArrivalRecordDTO.setReceivableAmount(hrBillTotal.getTotal() == null ? BigDecimal.ZERO : hrBillTotal.getTotal());
        } else {
            hrArrivalRecordDTO.setBillInvoiceId(invoiceRecordList.get(0).getInvoiceId());
            Double totalAmount = invoiceRecordList.stream().mapToDouble(HrBillInvoiceRecord::getTotalAmount).sum();
            hrArrivalRecordDTO.setReceivableAmount(totalAmount == null ? BigDecimal.ZERO : new BigDecimal(totalAmount.toString()));
        }
        hrArrivalRecordService.createHrArrivalRecord(hrArrivalRecordDTO);
        List<HrBill> salaryList = hrBills.stream().filter(lst -> lst.getBillType().equals(BillEnum.BillType.SALARY_BILL.getKey())).collect(Collectors.toList());
        //社会治理并且对应的结算单中存在薪酬账单
        if (rootParentClient.getId().equals(SpecialBillClient.SOCIAL_GOVERNANCE.getKey()) && CollectionUtils.isNotEmpty(salaryList)) {
            // 用于开票的结算单没有报销,用于工资的结算单没有开票有报销
            if (hrBills.get(0).getBillPurpose() == 2) {
                this.saveHrBillReimbursementApply(roleTemp, hrBillTotal, hrClient, hrBills, integer, invoiceRecordList);
            }
        } else {
            //添加报销申请
            this.saveHrBillReimbursementApply(roleTemp, hrBillTotal, hrClient, hrBills, integer, invoiceRecordList);
        }
    }

    @Override
    public String downloadInvoiceApprove(List<String> ids) {
        List<HrBillInvoiceDTO> hrBillInvoiceDTOS = hrBillInvoiceRepository.getBillInvoiceByIds(ids);
        List<HrBillInvoiceRecord> hrBillInvoiceRecords = hrBillInvoiceRecordService.list(new QueryWrapper<HrBillInvoiceRecord>().in("invoice_id", ids).eq("is_delete", 0));
        List<HrBillInvoiceRecordDTO> hrBillInvoiceRecordDTOS = hrBillInvoiceRecordMapper.toDto(hrBillInvoiceRecords);
        for (HrBillInvoiceDTO hrBillInvoiceDTO : hrBillInvoiceDTOS) {
            HrBillInvoice billInvoice = hrBillInvoiceRepository.selectById(hrBillInvoiceDTO.getId());
            List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceRecordDTOS.stream().filter(lst -> lst.getInvoiceId().equals(hrBillInvoiceDTO.getId())).collect(Collectors.toList());
            // 如果是新增那种，并且用户选择合并金额，需要把同类内容的合并
            if (billInvoice.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.NEWLY_ADDED.getKey()) && billInvoice.getIsMerge().equals(1)) {
                Set<String> contentTaxSet = new LinkedHashSet<>();
                invoiceRecords.forEach(item -> {
                    contentTaxSet.add(item.getContent() + "-" + item.getTaxRate());
                });
                Map<String, List<HrBillInvoiceRecordDTO>> map = invoiceRecords.stream().collect(Collectors.groupingBy(item -> (item.getContent() + "-" + item.getTaxRate())));
                List<HrBillInvoiceRecordDTO> invoiceRecords2 = new ArrayList<>();
                contentTaxSet.forEach(contentTax -> {
                    HrBillInvoiceRecordDTO billInvoiceRecordDTO = new HrBillInvoiceRecordDTO();
                    List<HrBillInvoiceRecordDTO> list = map.get(contentTax);
                    HrBillInvoiceRecordDTO first = list.get(0);
                    Double totalAmount = 0.00d;
                    billInvoiceRecordDTO.setContent(first.getContent());
                    billInvoiceRecordDTO.setTaxRate(first.getTaxRate());
                    for (HrBillInvoiceRecordDTO recordDTO : list) {
                        totalAmount += recordDTO.getTotalAmount();
                    }
                    billInvoiceRecordDTO.setTitle(first.getTitle());
                    billInvoiceRecordDTO.setTotalAmount(totalAmount);
                    billInvoiceRecordDTO.setTaxAmount(totalAmount * first.getTaxRate());
                    billInvoiceRecordDTO.setNoTaxAmount(billInvoiceRecordDTO.getTotalAmount() - billInvoiceRecordDTO.getTaxAmount());
                    invoiceRecords2.add(billInvoiceRecordDTO);
                });
                hrBillInvoiceDTO.setInvoiceRecords(invoiceRecords2);
            } else {
                hrBillInvoiceDTO.setInvoiceRecords(invoiceRecords);
            }
        }
        List<File> fileList = new ArrayList<>();
        List<String> temporary = new ArrayList<>();
        for (HrBillInvoiceDTO hrBillInvoiceDTO : hrBillInvoiceDTOS) {
            String invoiceFilePath = this.generateBillInvoice(hrBillInvoiceDTO);
            fileList.add(new File(invoiceFilePath));
            //加载Excel文档
            Workbook wb = new Workbook();
            wb.loadFromFile(invoiceFilePath);
            //调用方法保存为PDF格式
            String pdfName = tempPath + hrBillInvoiceDTO.getClientName() + "开票申请单_" + System.currentTimeMillis() + ".pdf";
            wb.setCustomFontFilePaths(new String[]{fontPath});
            wb.saveToFile(pdfName, FileFormat.PDF);
            fileList.add(new File(pdfName));
            temporary.add(pdfName);
        }
        String uploadFile = this.hrAppendixService.zipAndUploadFile(fileList, "开票申请单");
        temporary.add(uploadFile);
        temporary.forEach(lst -> {
            FileUtil.deleteTempFile(lst);
        });
        return uploadFile;
    }


    /**
     * 生成开票申请单
     *
     * @param hrBillInvoiceDTO
     * @return
     */
    private String generateBillInvoice(HrBillInvoiceDTO hrBillInvoiceDTO) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String createdDate = formatter.format(hrBillInvoiceDTO.getCreatedDate());
        XSSFWorkbook wb = new XSSFWorkbook();
        ZlSheet sheet = new ZlSheet(wb);
        sheet.nextRow(62)
            .createCell(sheet.newMergeCell(12, "开票申请单", true, (short) 20, true).setBackground(IndexedColors.BLUE))
            .nextRow(62)
            .createNextCell(sheet.newCell("公司", false, 30, (short) 14).setBackground(IndexedColors.BLUE))
            .createCell(6, "青岛市黄岛区人力资源有限公司", false, (short) 14)
            .createCell(8, "申请人", false, (short) 14)
            .createCell(12, hrBillInvoiceDTO.getApplyName() == null ? "" : hrBillInvoiceDTO.getApplyName(), false, (short) 15)
            .nextRow(62)
            .createNextCell(sheet.newCell("开票单位", false, 30, (short) 14).setBackground(IndexedColors.BLUE))
            .createCell(6, hrBillInvoiceDTO.getClientName(), false, (short) 14)
            .createCell(8, "申请时间", false, (short) 14)
            .createCell(12, createdDate, false, (short) 14)
            .nextRow(30)
            .createNextCell("摘要", false, (short) 13)
            .createCell(4, "发票内容", false, (short) 13)
            .createCell(6, "含税金额", false, (short) 13)
            .createCell(8, "不含税金额", false, (short) 13)
            .createCell(10, "税额", false, (short) 13)
            .createCell(12, "税率", false, (short) 13);
        List<HrBillInvoiceRecordDTO> invoiceRecords = hrBillInvoiceDTO.getInvoiceRecords();
        for (HrBillInvoiceRecordDTO invoiceRecord : invoiceRecords) {
            sheet.nextRow(45)
                .createNextCell(invoiceRecord.getTitle() == null ? "" : invoiceRecord.getTitle(), false, (short) 13)
                .createCell(4, invoiceRecord.getContent() == null ? "" : BillInvoiceApproveEnums.InvoiceContent.getValueByKey(invoiceRecord.getContent()), false, (short) 13)
                .createCell(6, invoiceRecord.getTotalAmount(), false, (short) 13)
                .createCell(8, invoiceRecord.getNoTaxAmount(), false, (short) 13)
                .createCell(10, invoiceRecord.getTaxAmount(), false, (short) 13)
                .createCell(12, invoiceRecord.getTaxRate() * 100 + "%", false, (short) 13);
        }
        double totalAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
        double taxAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTaxAmount())).getSum();
        double noTaxAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getNoTaxAmount())).getSum();
        double taxRate = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTaxRate())).getSum();
        List<Integer> integers = Arrays.asList(
            BillInvoiceApproveEnums.ON_ACCOUNTING.getKey(),
            BillInvoiceApproveEnums.ON_FINANCIAL_DIRECTOR.getKey(),
            BillInvoiceApproveEnums.ON_ACCOUNTING_CONFIRM.getKey(),
            BillInvoiceApproveEnums.SUCCESS.getKey());
        HSSFRichTextString hssfRichTextString = null;
        if (integers.contains(hrBillInvoiceDTO.getApproveStatus())) {
            List<UserDTO> userDTOList = userRepository.getByRoles(Arrays.asList(UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey()));
            if (CollectionUtils.isNotEmpty(userDTOList)) {
                List<String> userId = userDTOList.stream().map(UserDTO::getId).collect(Collectors.toList());
                //获取审批时间
                HrApplyOpLogs serviceOne = applyOpLogsService.getOne(new QueryWrapper<HrApplyOpLogs>().eq("apply_id", hrBillInvoiceDTO.getId())
                    .in("checker_id", userId).orderByDesc("created_date").last("LIMIT 1"));
                if (serviceOne != null) {
                    UserDTO userDTO = userDTOList.stream().filter(ls -> ls.getId().equals(serviceOne.getCheckerId())).findAny().orElse(null);
                    hssfRichTextString = new HSSFRichTextString(userDTO == null ? "" : userDTO.getRealName() + "\r\n\n" + formatter.format(serviceOne.getCreatedDate()));
                }
            }
        }
        sheet.nextRow(30)
            .createCell(4, "合计", false, 13, (short) 13)
            .createCell(6, totalAmount, false, (short) 13)
            .createCell(8, noTaxAmount, false, (short) 13)
            .createCell(10, taxAmount, false, (short) 13)
            .createCell(12, taxRate * 100 + "%", false, (short) 13)
            .nextRow(30)
            .createCell(12, "", false, (short) 13)
            .nextRow(60)
            .createNextCell("部门经理", false, 30, (short) 13)
            .createCell(12, hssfRichTextString == null ? "" : hssfRichTextString, false, (short) 13)
            .nextRow(130)
            .createCell(12, "", false, (short) 13);
        String filePath = sheet.writeFile(tempPath + hrBillInvoiceDTO.getClientName() + "开票申请单_" + System.currentTimeMillis() + ".xlsx");
        return filePath;
    }

    /**
     * 添加报销申请
     *
     * @param roleTemp
     * @param billTotal
     * @param hrBills
     * @param integer           0结算单审核 1开票单审核
     * @param invoiceRecordList
     */
    private void saveHrBillReimbursementApply(HrFeeReview roleTemp, HrBillTotal billTotal, HrClient hrClient, List<HrBill> hrBills, Integer integer, List<HrBillInvoiceRecord> invoiceRecordList) {
        JWTUserDTO curUser = SecurityUtils.getJwtUser();

        HrBillReimbursementApplyDTO result = new HrBillReimbursementApplyDTO();
        // 此处的bill_id为结算单id
        result.setBillId(roleTemp.getId())
            .setClientId(roleTemp.getClientId())
            .setClientName(hrClient.getClientName())
            .setTitle(hrClient.getClientName() + (roleTemp.getPayMonthly() > 9 ? "" : "0") + roleTemp.getPayMonthly() + "月份资金发放申请")
            .setPayYear(roleTemp.getPayYear())
            .setPayMonth(roleTemp.getPayMonthly())
            .setApproveStatus(BillReimbApproveEnums.NOT_LAUNCH.getKey())
            .setApplyDate(LocalDate.now())
            .setFlag(false)
            .setReimbursementState(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())
            .setReimbursementLockState(BillInvoiceApproveEnums.InvoiceLockState.NOT_LOCKED.getKey());
        HrBillTotalDTO billTotalDTO = hrBillTotalMapper.toDto(billTotal);
        HrClient rootParentClient = hrClientRepository.getRootParentClient(result.getClientId());
        List<HrBillReimbursementClient> reimbursementClient = new ArrayList<>();
        //处理特殊客户添加报销单
        if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.POLITICS_LAW_COMMITTEE.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.SECONDARY_POLITICS_LAW_COMMITTEE.getKey())
        ) {
            result.setId(RandomUtil.generateId());
            result.setAccountType(BillReimbApproveEnums.InvoiceTypeEnum.SPECIAL_CLIENT.getKey());
            reimbursementClient = this.createReimbursementClient(result.getId(), roleTemp.getId(), hrBills, billTotalDTO, rootParentClient);
        }
        List<HrBillReimbursementApplyDetailDTO> detailDTOS = this.hrBillReimbursementApplyService.assignmentDetailDTOList(billTotalDTO, result, hrBills, integer, invoiceRecordList, rootParentClient);
        result.setDetailDTOList(detailDTOS);
        HrBillReimbursementApplyDTO hrBillReimbursementApply = hrBillReimbursementApplyService.createHrBillReimbursementApply(result);
        if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.POLITICS_LAW_COMMITTEE.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.EAST_DISTRICT_PUBLIC_SECURITY.getKey())
            || rootParentClient.getId().equals(SpecialBillClient.SECONDARY_POLITICS_LAW_COMMITTEE.getKey())
        ) {
            List<HrBillReimbursementApplyDetailDTO> detailDTOList = hrBillReimbursementApplyDetailRepository.getByApplyId(hrBillReimbursementApply.getId());
            for (HrBillReimbursementApplyDetailDTO detailDTO : detailDTOList) {
                reimbursementClient.forEach(lst -> {
                    HrBillReimbursementClientDTO entry = new HrBillReimbursementClientDTO();
                    entry.setId(RandomUtil.generateId())
                        .setDetailId(detailDTO.getId())
                        .setClientBillId(lst.getId())
                        .setCreatedBy(curUser.getUserName())
                        .setCreatedDate(LocalDateTime.now());
                    hrBillReimbursementApplyDetailRepository.insertDetailClient(entry);
                });
            }
        }

        //海尔报销创建两个报销单 一个是社保医保公积金，一个是工资报销单
        if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
            result.setId(RandomUtil.generateId()).setAccountType(null);
            List<HrBillReimbursementApplyDetailDTO> detailDTOList = this.hrBillReimbursementApplyService.assignmentSalaryDetailList(billTotalDTO, result);
            result.setDetailDTOList(detailDTOList);
            hrBillReimbursementApplyService.createHrBillReimbursementApply(result);
        }
    }

    private List<HrBillReimbursementClient> createReimbursementClient(String applyId, String feeReviewId, List<HrBill> hrBills, HrBillTotalDTO totalDTO, HrClient rootParentClient) {
        List<String> billIds = hrBills.stream().map(HrBill::getId).collect(Collectors.toList());
        List<HrBillTotalDTO> hrBillTotalDTOS = hrBillTotalRepository.getBillTotalByBillId(billIds);
        Map<String, List<HrBillTotalDTO>> listMap = hrBillTotalDTOS.stream().collect(Collectors.groupingBy(HrBillTotalDTO::getClientId));
        List<HrBillTotalDTO> hrBillTotalDTOList = new ArrayList<>();
        for (Map.Entry<String, List<HrBillTotalDTO>> stringListEntry : listMap.entrySet()) {
            List<HrBillTotalDTO> billTotalDTOList = stringListEntry.getValue();
            HrBillTotalDTO hrBillTotalDTO = billTotalDTOList.stream().filter(lst -> lst.getType().equals(BillEnum.BillType.SECURITY_BILL.getKey())).findFirst().orElse(new HrBillTotalDTO());
            BigDecimal realSalaryTotal = billTotalDTOList.stream().filter(lst -> lst.getType().equals(BillEnum.BillType.SALARY_BILL.getKey())).map(HrBillTotalDTO::getRealSalaryTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            hrBillTotalDTO.setClientId(stringListEntry.getKey());
            hrBillTotalDTO.setRealSalaryTotal(realSalaryTotal);
            hrBillTotalDTOList.add(hrBillTotalDTO);
        }
        List<HrBillReimbursementClient> list = new ArrayList<>();
        for (HrBillTotalDTO billTotalDTO : hrBillTotalDTOList) {
            HrBillReimbursementClient hrBillReimbursementClient = new HrBillReimbursementClient();
            //(单位+个人)养老、(单位+个人)失业、(单位)工伤、(单位)补充工伤在申请报销时都将并入代缴社保；
            BigDecimal socialSecurityTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitPensionTotal(),
                billTotalDTO.getUnitUnemploymentTotal(),
                billTotalDTO.getWorkInjuryTotal(),
                billTotalDTO.getReplenishWorkInjuryExpenseTotal(),
                billTotalDTO.getPersonalPensionTotal(),
                billTotalDTO.getPersonalUnemploymentTotal());
            //(单位+个人)医疗、(单位)生育、(单位+个人)大额医疗在申请报销时都将并入代缴医保。
            BigDecimal medicalInsuranceTotal = CalculateUtils.decimalListAddition(billTotalDTO.getUnitMedicalTotal(),
                billTotalDTO.getPersonalMedicalTotal(),
                billTotalDTO.getUnitMaternityTotal(),
                billTotalDTO.getUnitLargeMedicalExpenseTotal(),
                billTotalDTO.getPersonalLargeMedicalExpenseTotal());
            hrBillReimbursementClient.setApplyId(applyId)
                .setFeeReviewId(feeReviewId)
                .setClientId(billTotalDTO.getClientId())
                .setBillId(billTotalDTO.getBillId())
                .setRealSalaryAmount(billTotalDTO.getRealSalaryTotal())
                .setSocialSecurityAmount(socialSecurityTotal)
                .setMedicalInsuranceAmount(medicalInsuranceTotal)
                .setAccumulationFoundAmount(billTotalDTO.getAccumulationFundTotal());
            if (rootParentClient.getId().equals(SpecialBillClient.HAIER.getKey())) {
                hrBillReimbursementClient
                    .setRealSalaryAmount(BigDecimal.ZERO)
                    .setRealSalaryLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
            }
            //每一项锁定状态
            List<HrBillReimbursementApplyDTO> dtoList = hrBillReimbursementApplyRepository.getByBillId(billTotalDTO.getBillId());
            if (CollectionUtils.isNotEmpty(dtoList)) {
                for (HrBillReimbursementApplyDTO applyDTO : dtoList) {
                    if (applyDTO.getAccountType() != null) {
                        BillReimbApproveEnums.InvoiceTypeEnum enumByKey = EnumUtils.getEnumByKey(BillReimbApproveEnums.InvoiceTypeEnum.class, applyDTO.getAccountType());
                        switch (enumByKey) {
                            case DF_SALARY:
                                totalDTO.setRealSalaryTotal(CalculateUtils.decimalSubtraction(totalDTO.getRealSalaryTotal(), billTotalDTO.getRealSalaryTotal()));
                                hrBillReimbursementClient.setRealSalaryLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                                break;
                            case DF_SOCIAL_SECURITY:
                                hrBillReimbursementClient.setSocialSecurityLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                                totalDTO.setUnitPensionTotal(CalculateUtils.decimalSubtraction(totalDTO.getUnitPensionTotal(), billTotalDTO.getUnitPensionTotal()));
                                totalDTO.setUnitUnemploymentTotal(CalculateUtils.decimalSubtraction(totalDTO.getUnitUnemploymentTotal(), billTotalDTO.getUnitUnemploymentTotal()));
                                totalDTO.setWorkInjuryTotal(CalculateUtils.decimalSubtraction(totalDTO.getWorkInjuryTotal(), billTotalDTO.getWorkInjuryTotal()));
                                totalDTO.setReplenishWorkInjuryExpenseTotal(CalculateUtils.decimalSubtraction(totalDTO.getReplenishWorkInjuryExpenseTotal(), billTotalDTO.getReplenishWorkInjuryExpenseTotal()));
                                totalDTO.setPersonalPensionTotal(CalculateUtils.decimalSubtraction(totalDTO.getPersonalPensionTotal(), billTotalDTO.getPersonalPensionTotal()));
                                totalDTO.setPersonalUnemploymentTotal(CalculateUtils.decimalSubtraction(totalDTO.getPersonalUnemploymentTotal(), billTotalDTO.getPersonalUnemploymentTotal()));
                                break;
                            case DF_MEDICAL_INSURANCE:
                                hrBillReimbursementClient.setMedicalInsuranceLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                                totalDTO.setUnitMedicalTotal(CalculateUtils.decimalSubtraction(totalDTO.getUnitMedicalTotal(), billTotalDTO.getUnitMedicalTotal()));
                                totalDTO.setPersonalMedicalTotal(CalculateUtils.decimalSubtraction(totalDTO.getPersonalMedicalTotal(), billTotalDTO.getPersonalMedicalTotal()));
                                totalDTO.setUnitMaternityTotal(CalculateUtils.decimalSubtraction(totalDTO.getUnitMaternityTotal(), billTotalDTO.getUnitMaternityTotal()));
                                totalDTO.setUnitLargeMedicalExpenseTotal(CalculateUtils.decimalSubtraction(totalDTO.getUnitLargeMedicalExpenseTotal(), billTotalDTO.getUnitLargeMedicalExpenseTotal()));
                                totalDTO.setPersonalLargeMedicalExpenseTotal(CalculateUtils.decimalSubtraction(totalDTO.getPersonalLargeMedicalExpenseTotal(), billTotalDTO.getPersonalLargeMedicalExpenseTotal()));
                                break;
                            case DF_ACCUMULATION_FOUND:
                                hrBillReimbursementClient.setAccumulationFoundLock(BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey());
                                totalDTO.setAccumulationFundTotal(CalculateUtils.decimalSubtraction(totalDTO.getAccumulationFundTotal(), billTotalDTO.getAccumulationFundTotal()));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            hrBillReimbursementClientRepository.insert(hrBillReimbursementClient);
            list.add(hrBillReimbursementClient);
        }
        return list;
    }

    /**
     * 会计确认
     *
     * @param id
     */
    @Override
    public void kjConfirm(String id) {
        // 获取当前用户
        JWTUserDTO userDTO = SecurityUtils.getJwtUser();
        HrBillInvoice hrBillInvoice = getById(id);
        if (!hrBillInvoice.getApproveStatus().equals(BillInvoiceApproveEnums.ON_ACCOUNTING_CONFIRM.getKey())) {
            throw new CommonException("当前流程节点不是会计确认，操作失败");
        }
        if (UserRoleTypeEnum.ACCOUNTING.getKey().equals(userDTO.getCurrentRoleKey())) {

            hrBillInvoice.setApproveStatus(BillInvoiceApproveEnums.SUCCESS.getKey());
            // todo 会计确认-调用用友nc生成凭证

            saveOrUpdate(hrBillInvoice);

            // 保存审批记录
            applyOpLogsService.saveHrApplyOpLogsPhaseTWO(hrBillInvoice.getId(), null, SecurityUtils.getCurrentUser().get().getId(),
                "会计确认!", null, ServiceCenterEnum.INVOICE_APPLY.getKey());

            // 发送通知
            this.sendMsg(hrBillInvoice, userDTO);
        } else {
            throw new CommonException("当前流程节点为会计确认，您无操作权限");
        }
    }

    /**
     * 获取可通知的角色列表
     *
     * @return
     */
    @Override
    public List<RoleDTO> getNoticeRoles() {
        List<String> roleKeys = Arrays.asList(
            UserRoleTypeEnum.CUSTOMER_SERVICE_MANAGER.getKey(),
            UserRoleTypeEnum.TOTAL_MANAGER.getKey(),
            UserRoleTypeEnum.ACCOUNTING.getKey(),
            UserRoleTypeEnum.CASHIER.getKey()
        );
        return this.roleRepository.getByRoleKeys(roleKeys);
    }

    /**
     * 生成外包客户开票信息
     *
     * @param clientId 外包客户ID
     * @return
     */
    @Override
    public HrBillInvoiceDTO generateHrBillInvoice(String clientId) {
        List<HrFeeReviewDTO> hrFeeReviewDTOList = hrFeeReviewRepository.selectFeeReviewByClientId(clientId);
        if (CollectionUtils.isEmpty(hrFeeReviewDTOList)) {
            throw new CommonException("该客户不存在可以开票的结算单信息！");
        }
        HrClient hrClient = hrClientRepository.selectById(clientId);
        HrProtocol hrProtocol = hrBillService.checkProtocol(clientId);

        HrBillInvoiceDTO hrBillInvoiceDTO = new HrBillInvoiceDTO();
        hrBillInvoiceDTO.setClientId(clientId)
            .setClientName(hrClient.getClientName())
            .setApplyDate(LocalDate.now());
        List<HrBillInvoiceRecordDTO> invoiceRecords = new ArrayList<>();
        Map<String, List<HrFeeReviewDTO>> listMap = hrFeeReviewDTOList.stream().collect(Collectors.groupingBy(HrFeeReviewDTO::getFeeReviewDate));
        listMap.keySet().forEach(paymentDate -> {
            List<String> reviewIds = listMap.get(paymentDate).stream().map(HrFeeReviewDTO::getId).collect(Collectors.toList());
            List<HrBillInvoiceDTO> hrBillInvoiceDTOS = hrBillInvoiceRepository.findByReviewId(reviewIds, null);
            BigDecimal accumulationFundTotalOld = BigDecimal.ZERO;
            BigDecimal socialSecurityTotalOld = BigDecimal.ZERO;
            BigDecimal serviceFeeTotalOld = BigDecimal.ZERO;
            BigDecimal preTaxSalaryOld = BigDecimal.ZERO;
            BigDecimal otherOld = BigDecimal.ZERO;
            BigDecimal totalOld = BigDecimal.ZERO;
            for (HrBillInvoiceDTO invoiceDTO : hrBillInvoiceDTOS) {
                if (!invoiceDTO.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())
                    && !invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())
                    && !invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.ALREADY_CANCEL.getKey())) {
                    List<HrBillInvoiceRecordDTO> recordDTOList = hrBillInvoiceRecordService.getByInvoiceId(invoiceDTO.getId());
                    for (HrBillInvoiceRecordDTO record : recordDTOList) {
                        if (record.getPaymentDate().equals(paymentDate)) {
                            BillInvoiceApproveEnums.InvoiceContent enumByKey = EnumUtils.getEnumByKey(BillInvoiceApproveEnums.InvoiceContent.class, record.getContent());
                            switch (enumByKey) {
                                case ACCUMULATION_FUND_INCOME:
                                    accumulationFundTotalOld = CalculateUtils.decimalAddition(accumulationFundTotalOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                case SOCIAL_SECURITY_INCOME:
                                    socialSecurityTotalOld = CalculateUtils.decimalAddition(socialSecurityTotalOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                case AGENCY_FEE_INCOME:
                                    serviceFeeTotalOld = CalculateUtils.decimalAddition(serviceFeeTotalOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                case WAGE_INCOME:
                                    preTaxSalaryOld = CalculateUtils.decimalAddition(preTaxSalaryOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                case OTHER_EXPENSES:
                                    otherOld = CalculateUtils.decimalAddition(otherOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                case HUMAN_RESOURCES_SERVICE_FEE:
                                    totalOld = CalculateUtils.decimalAddition(totalOld, new BigDecimal(record.getTotalAmount().toString()));
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
                /*if (invoiceDTO.getInvoiceState().equals(BillInvoiceApproveEnums.InvoiceState.OPENABLE_INVOICE.getKey())){
                    //判断可开发票是否已经发起开票记录，如果已发起的开票记录存在审核通过或者正在审核的记录则不统计结算单
                    List<Integer> neApproveStatusList = Arrays.asList(BillInvoiceApproveEnums.REJECT.getKey(), BillInvoiceApproveEnums.ALREADY_CANCEL.getKey());
                    List<HrBillInvoiceDTO> invoiceDTOList = hrBillInvoiceRepository.selectInfoList(invoiceDTO.getId(), neApproveStatusList);
                    if (!invoiceDTOList.isEmpty()){
                        reviewIds.remove(invoiceDTO.getFeeReviewId());
                    }
                }else {
                    if (invoiceDTO.getIsDefault().equals(BillInvoiceApproveEnums.InvoiceIsDefault.PROCESS_AUTOMATION.getKey())){
                        if (invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.SUCCESS.getKey())) {
                            //不统计开票审核通过的结算单那
                            reviewIds.remove(invoiceDTO.getFeeReviewId());
                        } else if (invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())) {
                            //不统计审核拒绝但是重新发起的结算单
                            List<HrBillInvoiceRecord> invoiceRecordList = hrBillInvoiceRecordService.list(new QueryWrapper<HrBillInvoiceRecord>()
                                .eq("invoice_id", invoiceDTO.getId())
                                .in("state", BillInvoiceApproveEnums.InvoiceRecordState.LOCKED.getKey(), BillInvoiceApproveEnums.InvoiceRecordState.AGAIN_LOCKED.getKey()));
                            if (invoiceRecordList != null && !invoiceRecordList.isEmpty()){
                                reviewIds.remove(invoiceDTO.getFeeReviewId());
                            }
                        }
                    } else {
                        if (!invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.REJECT.getKey())
                            && !invoiceDTO.getApproveStatus().equals(BillInvoiceApproveEnums.ALREADY_CANCEL.getKey())){
                            List<HrBillInvoiceRecordDTO> recordDTOList = hrBillInvoiceRecordService.getByInvoiceId(invoiceDTO.getId());
                            for (HrBillInvoiceRecordDTO record : recordDTOList) {
                                if (record.getPaymentDate().equals(paymentDate)){
                                    BillInvoiceApproveEnums.InvoiceContent enumByKey = EnumUtils.getEnumByKey(BillInvoiceApproveEnums.InvoiceContent.class, record.getContent());
                                    switch (enumByKey){
                                        case ACCUMULATION_FUND_INCOME: accumulationFundTotalOld = CalculateUtils.decimalAddition(accumulationFundTotalOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        case SOCIAL_SECURITY_INCOME: socialSecurityTotalOld = CalculateUtils.decimalAddition(socialSecurityTotalOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        case AGENCY_FEE_INCOME: serviceFeeTotalOld = CalculateUtils.decimalAddition(serviceFeeTotalOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        case WAGE_INCOME: preTaxSalaryOld = CalculateUtils.decimalAddition(preTaxSalaryOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        case OTHER_EXPENSES: otherOld = CalculateUtils.decimalAddition(otherOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        case HUMAN_RESOURCES_SERVICE_FEE: totalOld = CalculateUtils.decimalAddition(totalOld, new BigDecimal(record.getTotalAmount().toString())); break;
                                        default:break;
                                    }
                                }
                            }
                        }
                    }
                }*/
            }
            if (!reviewIds.isEmpty()) {
                String[] split = paymentDate.split("-");
                Integer payYear = Integer.parseInt(split[0]);
                Integer payMonthly = Integer.parseInt(split[1]);
                HrBillTotalDTO hrBillTotal = hrBillTotalRepository.getBillTotalByBatchBill(reviewIds);
                BigDecimal accumulationFundTotalNew;
                BigDecimal socialSecurityTotalNew;
                BigDecimal serviceFeeTotalNew;
                BigDecimal preTaxSalaryNew;
                BigDecimal otherNew;
                BigDecimal totalNew;
                //按分项金额开票
                if (hrProtocol.getInvoiceType() == 1) {
                    accumulationFundTotalNew = hrBillTotal.getAccumulationFundTotal() == null ? BigDecimal.ZERO : CalculateUtils.decimalSubtraction(hrBillTotal.getAccumulationFundTotal(), accumulationFundTotalOld);
                    if (!BigDecimalCompare.of(accumulationFundTotalNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, accumulationFundTotalNew, BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.ACCUMULATION_FUND_INCOME.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }
                    socialSecurityTotalNew = hrBillTotal.getSocialSecurityTotal() == null ? BigDecimal.ZERO : CalculateUtils.decimalSubtraction(hrBillTotal.getSocialSecurityTotal(), socialSecurityTotalOld);
                    if (!BigDecimalCompare.of(socialSecurityTotalNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, socialSecurityTotalNew, BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.SOCIAL_SECURITY_INCOME.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }
                    BigDecimal bigDecimal = CalculateUtils.decimalAddition(hrBillTotal.getServiceFeeTotal(), hrBillTotal.getTaxationFeeTotal());
                    serviceFeeTotalNew = CalculateUtils.decimalSubtraction(bigDecimal, serviceFeeTotalOld);
                    if (!BigDecimalCompare.of(serviceFeeTotalNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, serviceFeeTotalNew, BillInvoiceApproveEnums.InvoiceContent.AGENCY_FEE_INCOME.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.AGENCY_FEE_INCOME.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }
                    BigDecimal preTaxSalary = CalculateUtils.decimalAddition(hrBillTotal.getPersonalTaxTotal(), hrBillTotal.getRealSalaryTotal());
                    preTaxSalaryNew = CalculateUtils.decimalSubtraction(preTaxSalary, preTaxSalaryOld);
                    if (!BigDecimalCompare.of(preTaxSalaryNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, preTaxSalaryNew, BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.WAGE_INCOME.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }

                    BigDecimal otherDecimal = CalculateUtils.decimalSubtraction(hrBillTotal.getChargeTotal(), hrBillTotal.getRefundTotal());
                    otherNew = CalculateUtils.decimalSubtraction(otherDecimal, otherOld);
                    if (!BigDecimalCompare.of(otherNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, otherNew, BillInvoiceApproveEnums.InvoiceContent.OTHER_EXPENSES.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.OTHER_EXPENSES.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }
                }
                // 按总金额开票
                else {
                    totalNew = hrBillTotal.getTotal() == null ? BigDecimal.ZERO : CalculateUtils.decimalSubtraction(hrBillTotal.getTotal(), totalOld);
                    if (!BigDecimalCompare.of(totalNew).eq(BigDecimal.ZERO)) {
                        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = this.assignmentBillInvoiceRecord(hrClient, totalNew, BillInvoiceApproveEnums.InvoiceContent.HUMAN_RESOURCES_SERVICE_FEE.getKey(),
                            BillInvoiceApproveEnums.InvoiceContent.HUMAN_RESOURCES_SERVICE_FEE.getValue(), payYear, payMonthly);
                        hrBillInvoiceRecordDTO.setFeeReviewIds(reviewIds);
                        invoiceRecords.add(hrBillInvoiceRecordDTO);
                    }
                }
            }
        });
        hrBillInvoiceDTO.setInvoiceRecords(invoiceRecords);
        double totalAmount = invoiceRecords.stream().collect(Collectors.summarizingDouble(value -> value.getTotalAmount())).getSum();
        hrBillInvoiceDTO.setTotalAmount(totalAmount);
        hrBillInvoiceDTO.setTotalAmountCn(Convert.digitToChinese(totalAmount));
        hrBillInvoiceDTO.setTaxAmount(Double.valueOf("0"));
        return hrBillInvoiceDTO;
    }

    /**
     * 赋值账单开票明细
     *
     * @param hrClient    客户信息
     * @param totalAmount 金额
     * @param content     发票内容
     * @param value       发票内容名称
     * @param payYear     费用年
     * @param payMonthly  费用月
     * @return 开票明细
     */
    @Override
    public HrBillInvoiceRecordDTO assignmentBillInvoiceRecord(HrClient hrClient, BigDecimal totalAmount, String content, String value, Integer payYear, Integer payMonthly) {
        HrBillInvoiceRecordDTO hrBillInvoiceRecordDTO = new HrBillInvoiceRecordDTO();
        hrBillInvoiceRecordDTO.setTitle(hrClient.getClientName() + value);
        hrBillInvoiceRecordDTO.setContent(content);
        hrBillInvoiceRecordDTO.setTotalAmount(totalAmount.doubleValue());
        hrBillInvoiceRecordDTO.setNoTaxAmount(totalAmount.doubleValue());
        hrBillInvoiceRecordDTO.setTaxRate(BigDecimal.ZERO.doubleValue());
        hrBillInvoiceRecordDTO.setTaxAmount(BigDecimal.ZERO.doubleValue());
        hrBillInvoiceRecordDTO.setPayYear(payYear);
        hrBillInvoiceRecordDTO.setPayMonthly(payMonthly);
        hrBillInvoiceRecordDTO.setPaymentDate(payYear + "-" + (payMonthly > 9 ? payMonthly : "0" + payMonthly));
        return hrBillInvoiceRecordDTO;
    }

    /**
     * 处理开票结算单中间表
     */
    @Override
    public void invoiceReview() {
        List<HrBillInvoice> list = hrBillInvoiceRepository.selectList(new QueryWrapper<HrBillInvoice>().isNotNull("bill_id"));
        list.forEach(hrBillInvoice -> {
            HrBillInvoiceReview hrBillInvoiceReview = new HrBillInvoiceReview();
            hrBillInvoiceReview.setInvoiceId(hrBillInvoice.getId()).setFeeReviewId(hrBillInvoice.getBillId()).setCreatedDate(LocalDateTime.now());
            hrBillInvoiceReviewRepository.insert(hrBillInvoiceReview);
        });
    }
}
