package cn.casair.service.impl;

import cn.casair.common.errors.CommonException;
import cn.casair.ncc.dto.NccVoucherDTO;
import cn.casair.dto.nc.param.VoucherDetailDTO;
import cn.casair.dto.nc.param.VoucherHeadDTO;
import cn.casair.dto.nc.result.NcResultDTO;
import cn.casair.dto.nc.result.NccCustomerDTO;
import cn.casair.service.NcService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NC系统服务实现类
 *
 * <AUTHOR>
 * @since 2020-04-10
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class NcServiceImpl implements NcService {

    // 常量定义
    private static final String DEFAULT_ACCOUNT_BOOK = "0901-0001";
    private static final String ERROR_CODE_FAILURE = "1";
    private static final String BASE_PATH = "/uapws/rest/service";

    // 接口端点常量
    private static final String ENDPOINT_QRY_ACCOUNT_AUXILIARY = "/qryAccountAuxiliary";
    private static final String ENDPOINT_QRY_CUST_SUPP = "/qryCustSupp";
    private static final String ENDPOINT_CREATE_VOUCHER = "/createVoucher";

    private final RestTemplate restTemplate;
    private final String ncDomain;

    public NcServiceImpl(@Qualifier("ncRestTemplate") RestTemplate restTemplate,
                         @Value("${nc.domain}") String ncDomain) {
        this.restTemplate = restTemplate;
        this.ncDomain = ncDomain;
    }

    /**
     * 创建JSON请求头
     */
    private HttpHeaders createJsonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    /**
     * 构建NC系统接口URL
     */
    private String buildUrl(String endpoint) {
        return UriComponentsBuilder.fromHttpUrl(ncDomain)
            .path(BASE_PATH)
            .path(endpoint)
            .toUriString();
    }

    /**
     * 处理NC系统响应结果
     */
    private void handleNcResponse(NcResultDTO<?> response, String operation) {
        if (ERROR_CODE_FAILURE.equals(response.getErrorNo())) {
            log.error("{}失败，原因：{}", operation, response.getMessage());
            throw new CommonException(response.getMessage());
        }
    }

    /**
     * 执行NC系统请求的通用方法
     */
    private <T> T executeNcRequest(String url, Object requestBody, Class<T> responseType, String operation) {
        try {
            HttpEntity<Object> request = new HttpEntity<>(requestBody, createJsonHeaders());
            log.info("调用NC系统{}接口，参数：{}", operation, JSON.toJSONString(requestBody));

            T response = restTemplate.postForObject(url, request, responseType);
            log.info("调用NC系统{}接口成功，返回：{}", operation, JSON.toJSONString(response));

            return response;
        } catch (RestClientException e) {
            log.error("调用NC系统{}接口失败，参数：{}，错误：{}", operation, JSON.toJSONString(requestBody), e.getMessage(), e);
            throw new CommonException(String.format("调用NC系统%s接口失败！", operation));
        } catch (Exception e) {
            log.error("调用NC系统{}接口发生未知错误，参数：{}，错误：{}", operation, JSON.toJSONString(requestBody), e.getMessage(), e);
            if (e instanceof HttpStatusCodeException) {
                log.error("HTTP状态码错误详情：{}", ((HttpStatusCodeException) e).getResponseBodyAsString());
            }
            throw new CommonException(String.format("调用NC系统%s接口失败！", operation));
        }
    }

    private <T> T executeNcRequestWithTypeRef(String url, Object requestBody, ParameterizedTypeReference<T> typeRef, String operation) {
        try {
            HttpEntity<Object> request = new HttpEntity<>(requestBody, createJsonHeaders());
            log.info("调用NC系统{}接口，参数：{}", operation, JSON.toJSONString(requestBody));

            ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, typeRef);
            T response = responseEntity.getBody();
            log.info("调用NC系统{}接口成功，返回：{}", operation, JSON.toJSONString(response));

            return response;
        } catch (RestClientException e) {
            log.error("调用NC系统{}接口失败，参数：{}，错误：{}", operation, JSON.toJSONString(requestBody), e.getMessage(), e);
            throw new CommonException(String.format("调用NC系统%s接口失败！", operation));
        } catch (Exception e) {
            log.error("调用NC系统{}接口发生未知错误，参数：{}，错误：{}", operation, JSON.toJSONString(requestBody), e.getMessage(), e);
            if (e instanceof HttpStatusCodeException) {
                log.error("HTTP状态码错误详情：{}", ((HttpStatusCodeException) e).getResponseBodyAsString());
            }
            throw new CommonException(String.format("调用NC系统%s接口失败！", operation));
        }
    }
    /**
     * 查询会计科目及辅助
     *
     * @param code 科目编码
     * @param name 科目名称
     * @return 查询结果
     */
    @Override
    public Object qryAccountAuxiliary(String code, String name) {
        log.info("查询会计科目及辅助，参数 - code: {}, name: {}", code, name);
        // 设置请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("accountbook", DEFAULT_ACCOUNT_BOOK);
        if (StringUtils.isNotBlank(code)) {
            requestParams.put("code", code);
        }
        if (StringUtils.isNotBlank(name)) {
            requestParams.put("name", name);
        }

        String url = buildUrl(ENDPOINT_QRY_ACCOUNT_AUXILIARY);
        NcResultDTO<?> response = executeNcRequest(url, requestParams, NcResultDTO.class, "查询会计科目及辅助");

        handleNcResponse(response, "查询会计科目及辅助");
        return response.getData();
    }

    /**
     * 查询客户档案
     *
     * @param code 客户编码
     * @param name 客户名称
     * @return 查询结果
     */
    @Override
    public List<NccCustomerDTO> qryCustomer(String code, String name) {
        log.info("查询客户档案，参数 - code: {}, name: {}", code, name);

        // 设置请求参数
        Map<String, Object> requestParams = new HashMap<>();
        if (StringUtils.isNotBlank(code)) {
            requestParams.put("code", code);
        }
        if (StringUtils.isNotBlank(name)) {
            requestParams.put("name", name);
        }

        String url = buildUrl(ENDPOINT_QRY_CUST_SUPP);
        NcResultDTO<List<NccCustomerDTO>> response = executeNcRequestWithTypeRef(url, requestParams,
            new ParameterizedTypeReference<NcResultDTO<List<NccCustomerDTO>>>() {
            }, "查询客户档案");

        handleNcResponse(response, "查询客户档案");
        return response.getData();
    }

    /**
     * 生成凭证
     *
     * @param voucherHead       凭证头信息
     * @param voucherDetailList 凭证明细列表
     * @return NC系统响应结果
     */
    @Override
    public NcResultDTO<?> createVoucher(VoucherHeadDTO voucherHead, List<VoucherDetailDTO> voucherDetailList) {
        log.info("生成凭证，凭证头：{}, 明细数量：{}", voucherHead, voucherDetailList != null ? voucherDetailList.size() : 0);

        // 设置请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("voucher_head", voucherHead);
        requestParams.put("voucher_details", voucherDetailList);

        String url = buildUrl(ENDPOINT_CREATE_VOUCHER);
        return executeNcRequest(url, requestParams, NcResultDTO.class, "生成凭证");
    }

    @Override
    public NcResultDTO<NccVoucherDTO> createNccVoucher(NccVoucherDTO nccVoucherDTO) {
        log.info("生成ncc凭证：{}", nccVoucherDTO);
        String url = buildUrl(ENDPOINT_CREATE_VOUCHER);
        return executeNcRequestWithTypeRef(url, nccVoucherDTO, new ParameterizedTypeReference<NcResultDTO<NccVoucherDTO>>() {
        }, "生成ncc凭证");
    }
}
