package cn.casair.ncc;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import nccloud.open.api.auto.token.itf.IAPIUtils;
import nccloud.open.api.auto.token.old.utils.APIOldUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ncc配置
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "ncc")
public class NccConfiguration {
    /**
     * ip: 127.0.0.1
     * port: 8013
     * busiCenter: develop
     * appId: ncc1
     * appSecret: e64b98e4fe9e4d3f823a
     * publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt4Vjm27e2ZMw8wstEKZZsnvAFB13bnt2UA170Hw2Wpcv1zUnEuws8uE4l+MoADjwjwMgoiNoQN8S8IcIx0P+qmsc1zNkdqoVDX4V292z1Ul1ZZwPT2LhyATcF6AOWp/O2MitIFxBrFlMpVE8bvTGtOavtUYOkJWL1ZERp2/Cufv/Xe9wtuWuIxcSn97+YLf9ynsVMTr49g7yddJGuBuJRGGYj3FXDWiJG+xP5jO8ulIfgSe5+TwitJTMw3wS4xqEmJj+EG0AlEcD2wlbcHmB1Wx14DGRvZbQtBv3z0IG0DqtbZoQ1gUAyqJfYaZEgZwfutfN+3mcF+0sWU9K9zzr4QIDAQAB
     *
     * @return
     */
    private String ip;
    private String port;
    private String busiCenter;
    private String appId;
    private String appSecret;
    private String publicKey;

    @Bean
    public IAPIUtils iAPIUtils() {
        IAPIUtils iapiUtils = new APIOldUtils();
        log.info("NCC ip:{}, port:{}, busiCenter:{}, appId:{}, appSecret:{}, publicKey:{}", ip, port, busiCenter, appId, appSecret, publicKey);
        iapiUtils.init(ip, port, busiCenter, appId, appSecret, publicKey, null, null);
        return iapiUtils;
    }

}
