package cn.casair.ncc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 凭证传输对象（DTO）
 * 用于调用 NCC 凭证生成接口：POST http://ip:port/nccloud/api/gl/voucher/insert
 * 对应场景：外部系统生成 NCC 凭证
 */
@Data
public class NccVoucherDTO {

    /**
     * 核算账簿编码
     * 示例值：1000-0001
     * 是否必传：是
     */
    @JsonProperty("accbookCode")
    private String accbookCode;

    /**
     * 制单日期，格式：yyyy-MM-dd
     * 示例值：2023-01-12
     * 是否必传：是
     */
    @JsonProperty("prepareddate")
    private String prepareddate;

    /**
     * 会计年度
     * 示例值：2023
     * 是否必传：否
     */
    @JsonProperty("year")
    private String year;

    /**
     * 会计期间
     * 示例值：01
     * 是否必传：否
     */
    @JsonProperty("period")
    private String period;

    /**
     * 凭证类别编码
     * 示例值：01
     * 是否必传：否
     */
    @JsonProperty("vouchertype")
    private String vouchertype;

    /**
     * 制单人编码
     * 示例值：yonyou_zh
     * 是否必传：是
     */
    @JsonProperty("prepared")
    private String prepared;

    /**
     * 凭证号
     * 示例值：123
     * 是否必传：否（未传递,凭证号将由系统自动生成）
     */
    @JsonProperty("num")
    private Integer num;

    /**
     * 附单据数
     * 示例值：0
     * 是否必传：否
     */
    @JsonProperty("attachment")
    private Integer attachment;

    /**
     * 凭证分录列表
     * 每条分录包含科目、金额、摘要、辅助核算等信息
     * 是否必传：是（至少一条分录）
     */
    @JsonProperty("detail")
    private List<Detail> detail;

    /**
     * 凭证分录 DTO（内部类）
     */
    @Data
    public static class Detail {

        /**
         * 分录号（序号）
         * 示例值：1
         * 是否必传：否（未传递,分录号将由系统自动生成）
         */
        @JsonProperty("detailindex")
        private Integer detailindex;

        /**
         * 业务单元编码
         * 是否必传：否
         */
        @JsonProperty("unitCode")
        private String unitCode;

        /**
         * 摘要
         * 示例值：测试凭证
         * 是否必传：是
         */
        @JsonProperty("explanation")
        private String explanation;

        /**
         * 科目编码
         * 示例值：2002
         * 是否必传：是
         */
        @JsonProperty("accountCode")
        private String accountCode;

        /**
         * 币种编码
         * 示例值：CNY
         * 是否必传：是
         */
        @JsonProperty("currtypeCode")
        private String currtypeCode;

        /**
         * 数量
         * 是否必传：否
         */
        @JsonProperty("quantity")
        private Double quantity;

        /**
         * 原币金额
         * 示例值：100
         * 是否必传：是
         */
        @JsonProperty("amount")
        private BigDecimal amount;

        /**
         * 组织本币借方金额
         * 示例值：100
         * 是否必传：是
         */
        @JsonProperty("localdebitamount")
        private BigDecimal localdebitamount;

        /**
         * 组织本币贷方金额
         * 示例值：100
         * 是否必传：是
         */
        @JsonProperty("localcreditamount")
        private BigDecimal localcreditamount;

        /**
         * 集团本币借方金额
         * 示例值：100
         * 是否必传：否
         */
        @JsonProperty("groupdebitamount")
        private Double groupdebitamount;

        /**
         * 集团本币贷方金额
         * 示例值：100
         * 是否必传：否
         */
        @JsonProperty("groupcreditamount")
        private Double groupcreditamount;

        /**
         * 全局本币借方金额
         * 示例值：100
         * 是否必传：否
         */
        @JsonProperty("globaldebitamount")
        private Double globaldebitamount;

        /**
         * 全局本币贷方金额
         * 示例值：100
         * 是否必传：否
         */
        @JsonProperty("globalcreditamount")
        private Double globalcreditamount;

        /**
         * 组织本币汇率
         * 是否必传：否
         */
        @JsonProperty("locRate")
        private Double locRate;

        /**
         * 集团本币汇率
         * 是否必传：否
         */
        @JsonProperty("groupRate")
        private Double groupRate;

        /**
         * 全局本币汇率
         * 是否必传：否
         */
        @JsonProperty("globalRate")
        private Double globalRate;

        /**
         * 单价
         * 是否必传：否
         */
        @JsonProperty("price")
        private Double price;

        /**
         * 业务日期，格式：yyyy-MM-dd
         * 示例值：2023-01-12
         * 是否必传：是
         */
        @JsonProperty("busidate")
        private String busidate;

        /**
         * 自定义项1-10
         * 是否必传：否
         */
        @JsonProperty("freevalue1")
        private String freevalue1;
        @JsonProperty("freevalue2")
        private String freevalue2;


        /**
         * 利润中心编码
         * 是否必传：否
         */
        @JsonProperty("liabilitycenterCode")
        private String liabilitycenterCode;

        /**
         * 辅助核算项列表
         * 示例：客户、部门、项目等
         * 是否必传：否（根据科目是否启用辅助核算决定）
         */
        @JsonProperty("ass")
        private List<Ass> ass;

        /**
         * 现金流量项列表
         * 是否必传：否
         */
        @JsonProperty("cashflow")
        private List<Cashflow> cashflow;
    }

    /**
     * 辅助核算项 DTO（内部类）
     */
    @Data
    public static class Ass {

        /**
         * 辅助核算类型编码
         * 是否必传：是
         */
        @JsonProperty("checktypecode")
        private String checktypecode;

        /**
         * 辅助核算值编码
         * 是否必传：否
         */
        @JsonProperty("checkvaluecode")
        private String checkvaluecode;
    }

    /**
     * 现金流量 DTO（内部类）
     */
    @Data
    public static class Cashflow {

        /**
         * 内部单位编码
         * 是否必传：否
         */
        @JsonProperty("innercorpCode")
        private String innercorpCode;

        /**
         * 主表项编码
         * 是否必传：是
         */
        @JsonProperty("mainCode")
        private String mainCode;

        /**
         * 附表项编码
         * 是否必传：是
         */
        @JsonProperty("subCode")
        private String subCode;

        /**
         * 表项原币金额
         * 是否必传：是
         */
        @JsonProperty("amount")
        private Double amount;

        /**
         * 表项本币金额
         * 是否必传：是
         */
        @JsonProperty("localamount")
        private Double localamount;

        /**
         * 表项集团本币金额
         * 是否必传：是
         */
        @JsonProperty("groupamount")
        private Double groupamount;

        /**
         * 表项全局本币金额
         * 是否必传：是
         */
        @JsonProperty("globalamount")
        private Double globalamount;
    }
}
