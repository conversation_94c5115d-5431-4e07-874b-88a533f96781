package cn.casair.ncc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * NCC客户新增请求传输对象（DTO）
 * 用于调用 NCC 客户新增接口：POST http://ip:port/nccloud/api/uapbd/customermanage/customer/add
 * 对应场景：外部系统新增 NCC 客户
 */
@Data
public class NccCustomerRequestDTO {

    /**
     * 接口参数结构
     * 是否必传：是
     */
    @JsonProperty("ufinterface")
    private UfInterface ufinterface;

    /**
     * 接口参数结构 DTO（内部类）
     */
    @Data
    public static class UfInterface {

        /**
         * 账套编码
         * 示例值：NCCloud
         * 默认值：NCCloud
         * 是否必传：是
         */
        @JsonProperty("account")
        private String account = "NCCloud";

        /**
         * 单据类型
         * 示例值：customer
         * 默认值：customer
         * 是否必传：是
         */
        @JsonProperty("billtype")
        private String billtype = "customer";

        /**
         * 集团编码
         * 示例值：00
         * 默认值：00（xx集团）
         * 是否必传：是
         */
        @JsonProperty("groupcode")
        private String groupcode = "00";

        /**
         * 发送方编码
         * 示例值：default
         * 默认值：default
         * 是否必传：是
         */
        @JsonProperty("sender")
        private String sender = "default";

        /**
         * 客户详细信息
         * 是否必传：是
         */
        @JsonProperty("bill")
        private Bill bill;
    }

    /**
     * 客户详细信息 DTO（内部类）
     */
    @Data
    public static class Bill {

        /**
         * 客户详细信息
         * 是否必传：是
         */
        @JsonProperty("billhead")
        private BillHead billhead;
    }

    /**
     * 客户详细信息 DTO（内部类）
     */
    @Data
    public static class BillHead {

        /**
         * 所属集团
         * 示例值：00
         * 默认值：00（xx集团）
         * 是否必传：是
         */
        @JsonProperty("pk_group")
        private String pkGroup = "00";

        /**
         * 所属组织
         * 示例值：00
         * 默认值：00（xx集团）
         * 是否必传：是
         */
        @JsonProperty("pk_org")
        private String pkOrg = "00";

        /**
         * 客户名称
         * 示例值：kehhu1
         * 是否必传：是
         */
        @JsonProperty("name")
        private String name;

        /**
         * 客户基本分类
         * 示例值：01
         * 默认值：01
         * 是否必传：是
         */
        @JsonProperty("pk_custclass")
        private String pkCustclass = "01";

        /**
         * 国家/地区
         * 示例值：CN
         * 默认值：CN
         * 是否必传：是
         */
        @JsonProperty("pk_country")
        private String pkCountry = "CN";

        /**
         * 数据格式
         * 示例值：FMT0Z000000000000000
         * 默认值：FMT0Z000000000000000
         * 是否必传：是
         */
        @JsonProperty("pk_format")
        private String pkFormat = "FMT0Z000000000000000";

        /**
         * 时区
         * 示例值：P0800
         * 默认值：P0800
         * 是否必传：是
         */
        @JsonProperty("pk_timezone")
        private String pkTimezone = "P0800";

        /**
         * 客户类型
         * 示例值：0
         * 默认值：0
         * 是否必传：是
         */
        @JsonProperty("custprop")
        private String custprop = "0";

        /**
         * 客户编码
         * 示例值：kehu001
         * 备注：要与memo保持一致
         * 是否必传：否（根据示例推断）
         */
        @JsonProperty("code")
        private String code;

        /**
         * 备注
         * 示例值：kehu001
         * 备注：要与上面的code保持一致
         * 是否必传：否
         */
        @JsonProperty("memo")
        private String memo;

        /**
         * 是否零售店
         * 示例值：N
         * 是否必传：否（根据示例推断）
         */
        @JsonProperty("isretailstore")
        private String isretailstore;

        /**
         * 开户行
         * 示例值：开户行
         * 是否必传：否
         */
        @JsonProperty("def1")
        private String def1;

        /**
         * 账号
         * 示例值：账号
         * 是否必传：否
         */
        @JsonProperty("def2")
        private String def2;

        /**
         * 单位联系人
         * 示例值：单位联系人
         * 是否必传：否
         */
        @JsonProperty("def3")
        private String def3;

        /**
         * 联系方式
         * 示例值：联系方式
         * 是否必传：否
         */
        @JsonProperty("def4")
        private String def4;

        /**
         * 单位地址
         * 示例值：单位地址
         * 是否必传：否
         */
        @JsonProperty("def5")
        private String def5;
    }
}
